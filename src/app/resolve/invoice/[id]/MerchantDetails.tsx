'use client';

import { Box, Center, Text } from '@chakra-ui/react';
import { FaAngleDown } from 'react-icons/fa6';
export default function MerchantDetails({ userDetails }: { userDetails: any }) {
  return (
    <Box bg={'white'} rounded={'.625rem'} p={'1rem'} pb={'1rem'} h={'100%'}>
      <Text mb={'.625rem'} fontWeight={500}>
        Merchant details
      </Text>
      <Text fontSize={'.875rem'}>Email: {userDetails?.email}</Text>

      <Box mt={'.5rem'}>
        <Center>
          <FaAngleDown color="#6b6c72" />
        </Center>
      </Box>
    </Box>
  );
}
