'use client';

import {
  Box,
  Container,
  Flex,
  GridItem,
  Image,
  SimpleGrid,
  Stack,
  Text,
} from '@chakra-ui/react';
import logo from '@/assets/logo.png';

import PaymentForm from './PaymentForm';
import InvoiceDetails from './InvoiceDetails';
import MerchantDetails from './MerchantDetails';
export default function ResolveInvoicePayment({
  // id,
  clientSecret,
  // paymentLink,
  invoiceDetails,
  userDetails,
  paymentIntent,
}: {
  id: string;
  clientSecret: string;
  paymentLink: any;
  invoiceDetails: any;
  userDetails: any;
  paymentIntent: any;
}) {
  return (
    <div>
      <Container>
        <Flex alignItems={'center'} justifyContent={'space-between'} h={'5rem'}>
          <Image
            src={userDetails?.organization?.logo_url || logo.src}
            alt="logo"
            w={'5rem'}
          />
        </Flex>

        <SimpleGrid
          maxW={'6xl'}
          mt={'2rem'}
          gap={'1.25rem'}
          columns={{ base: 1, md: 3 }}
          mx={'auto'}
          pb={{ base: '2rem', md: 'unset' }}
        >
          <GridItem
            colSpan={{ base: 1, md: 2 }}
            bg={'white'}
            rounded={{ base: '.4rem', md: '.625rem' }}
            pt={{ base: '1rem', md: '2rem' }}
            pb={'1rem'}
            px={{ base: '1rem', md: '2rem' }}
          >
            <Text mb={'.25rem'} textTransform={'uppercase'} color={'gray.200'}>
              Payment amount
            </Text>
            <Text
              textTransform={'uppercase'}
              fontWeight={'600'}
              color={'gray.500'}
              fontSize={'1.4rem'}
            >
              {paymentIntent?.currency}${paymentIntent.amount / 100}
            </Text>

            <Box mt={'1.25rem'}>
              <Text mb={'.625rem'} fontSize={'.875rem'} fontWeight={500}>
                Pay with credit card
              </Text>
              <PaymentForm
                clientSecret={clientSecret}
                paymentIntent={paymentIntent}
              />
            </Box>
          </GridItem>
          <GridItem colSpan={1}>
            <Stack gap={'1rem'} h={'100%'}>
              <InvoiceDetails
                userDetails={userDetails}
                invoiceDetails={invoiceDetails}
              />
              <MerchantDetails userDetails={userDetails} />
            </Stack>
          </GridItem>
        </SimpleGrid>
      </Container>
    </div>
  );
}
