'use client';
import logo from '@/assets/logo.png';
import {
  Box,
  Center,
  Flex,
  Image,
  Separator,
  Stack,
  Text,
  useBreakpoint,
} from '@chakra-ui/react';
import moment from 'moment';
import { LuDownload } from 'react-icons/lu';
import { FiPrinter } from 'react-icons/fi';

import InvoicePdf from './InvoicePdf';
import { useState, useRef } from 'react';
import { pdf } from '@react-pdf/renderer';
import { PDFGenerator } from '@/components/elements/pdf/PDF-Generator';
import { formatMoney } from '@/components/elements/format-money/FormatMoney';
// import { useBreakpoint } from '@chakra-ui/react';

function Item({ label, value }: any) {
  return (
    <Flex alignItems={'center'} justifyContent={'space-between'}>
      <Text color={'gray.300'} fontSize={'.85rem'} fontWeight={400}>
        {label}
      </Text>
      <Text fontSize={'.85rem'} fontWeight={500}>
        {value}
      </Text>
    </Flex>
  );
}

export default function InvoiceDetails({ userDetails, invoiceDetails }: any) {
  const [open, setOpen] = useState(false);
  const pdfRef = useRef<HTMLDivElement>(null);

  const breakpoint = useBreakpoint({ ssr: false });
  const isMobile = breakpoint === 'base' || breakpoint === 'sm';
  const totalPaid = invoiceDetails?.transactions?.reduce(
    (prev: number, currentTransaction: any) => {
      return prev + Number(currentTransaction?.amount || 0);
    },
    0
  );
  const amountDue = Number(invoiceDetails?.total_price) - Number(totalPaid);
  const resolvedAmountDue = formatMoney(amountDue, {
    currencyCode: invoiceDetails?.currency_code,
  });
  // Common function to generate PDF document
  const generatePDFDocument: any = () => (
    <PDFGenerator
      name={String(
        invoiceDetails?.client?.display_name || invoiceDetails?.name
      )}
      email={String(
        invoiceDetails?.client?.client_emails?.[0]?.email ||
          invoiceDetails?.email
      )}
      receiptNumber={String(invoiceDetails?.invoice_number)}
      transactions={invoiceDetails?.transactions ?? []}
      date={String(
        moment(invoiceDetails?.invoice_date?.split('T')[0]).format(
          'MMMM D, YYYY'
        )
      )}
      dueDate={String(
        moment(
          invoiceDetails?.due_date?.split('T')[0] ||
            invoiceDetails?.invoice_date?.split('T')[0]
        ).format('MMMM D, YYYY')
      )}
      amountDue={amountDue}
      resolvedAmountDue={resolvedAmountDue}
      activity={String(invoiceDetails?.product || '')}
      invoice={invoiceDetails}
      referral={invoiceDetails?.referral ?? ''}
      quantity={Number(invoiceDetails?.qty || 0)}
      rate={Number(invoiceDetails?.total_price || 0)}
      balance={0}
      memo={String(invoiceDetails?.memo || '')}
    />
  );
  const handlePrint = async () => {
    const doc = generatePDFDocument();
    const blob = await pdf(doc).toBlob();
    const url = URL.createObjectURL(blob);

    // Create hidden iframe
    const iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    iframe.src = url;
    document.body.appendChild(iframe);

    iframe.onload = () => {
      iframe.contentWindow?.focus();
      iframe.contentWindow?.print();

      if (iframe.contentWindow) {
        // cleanup after print
        iframe.contentWindow.onafterprint = () => {
          document.body.removeChild(iframe);
          URL.revokeObjectURL(url);
        };
      }
    };
  };

  // Function to download PDF
  const handleDownload = async () => {
    const doc = generatePDFDocument();
    const blob = await pdf(doc).toBlob();
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `invoice-${invoiceDetails?.invoice_number || 'document'}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const handleViewInvoice = async () => {
    if (isMobile) {
      const doc = generatePDFDocument();
      const blob = await pdf(doc).toBlob();
      const url = URL.createObjectURL(blob);
      window.open(url, '_blank');
    } else {
      setOpen(true); // Use modal for non-mobile devices
    }
  };
  return (
    <Box bg={'white'} rounded={'.625rem'} p={'1rem'} pb={'1rem'}>
      <Image
        src={userDetails?.organization?.logo_url || logo.src}
        alt="logo"
        w={'5rem'}
      />
      <Stack gap={'.625rem'} mt={'.625rem'}>
        <Text fontWeight={500} fontSize={'1rem'}>
          {userDetails?.organization?.name}
        </Text>
        <Item label={'Invoice'} value={invoiceDetails?.invoice_number} />
        <Item
          label={'Due date'}
          value={moment(invoiceDetails.due_date).format('MMM D, YYYY')}
        />
        <Item
          label={'Invoice amount'}
          value={`${invoiceDetails?.currency_code}$${invoiceDetails?.total_price}`}
        />
      </Stack>
      <Separator mt={'.5rem'} mb={'.625rem'} borderColor={'gray.50'} />
      <Flex mb={'1rem'} alignItems={'center'} justifyContent={'space-between'}>
        <Text fontSize={'.85rem'} fontWeight={600}>
          Total
        </Text>
        <Text fontSize={'.85rem'}>
          {invoiceDetails?.currency_code}${invoiceDetails?.total_price}
        </Text>
      </Flex>
      <Flex alignItems={'center'} justifyContent={'space-between'}>
        <Center
          fontSize={'.75rem'}
          border={'solid 1px #6b6c72'}
          px={'.875rem'}
          py={'.2rem'}
          cursor={'pointer'}
          onClick={handleViewInvoice}
        >
          View invoice
        </Center>

        <Flex alignItems={'center'} gap={'1rem'}>
          <LuDownload
            size={'1.25rem'}
            cursor={'pointer'}
            onClick={handleDownload}
            title="Download PDF"
          />
          <FiPrinter
            size={'1.25rem'}
            cursor={'pointer'}
            onClick={handlePrint}
            title="Print Invoice"
          />
        </Flex>
      </Flex>
      {open && (
        <Box ref={pdfRef}>
          <InvoicePdf
            invoiceDetails={invoiceDetails}
            open={open}
            setOpen={setOpen}
          />
        </Box>
      )}
    </Box>
  );
}
