# Navigation Protection for SOAP Notes

## Overview

The SoapNoteComponent now includes comprehensive navigation protection to prevent users from losing unsaved changes. This protection works for both browser navigation (back/forward buttons, tab close, refresh) and internal app navigation.

## How It Works

### 1. Change Detection

The component tracks unsaved changes by comparing current form values with saved data:

```typescript
const hasUnsavedChanges = useMemo(() => {
  return (
    values?.soap_note !== booking?.slp_notes?.soap_note ||
    values?.invoice_memo !== booking?.slp_notes?.invoice?.memo ||
    values?.session_count !== booking?.slp_notes?.invoice?.qty
  );
}, [values, booking]);
```

### 2. Browser Navigation Protection

**beforeunload Event**: Prevents data loss when users try to:
- Close browser tab/window
- Navigate to external websites
- Refresh the page

**popstate Event**: Handles browser back/forward button navigation:
- Detects when user clicks back/forward
- Prevents navigation and shows confirmation dialog
- Maintains browser history state

### 3. Internal Navigation Protection

The component provides a `handleNavigationWithConfirmation` function that can be used by parent components to handle internal navigation with confirmation.

## Usage

### Basic Usage

```typescript
<SoapNoteComponent
  soapNoteHook={soapNoteHook}
  section="slp"
  abbr={timezone}
  templateHook={templateHook}
/>
```

### With Custom Navigation Handler

```typescript
const handleNavigation: NavigationHandler = (hasUnsavedChanges, navigationFn) => {
  if (hasUnsavedChanges) {
    // Show your custom confirmation dialog
    showCustomDialog({
      onConfirm: navigationFn,
      onCancel: () => {}
    });
  } else {
    navigationFn();
  }
};

<SoapNoteComponent
  soapNoteHook={soapNoteHook}
  section="slp"
  abbr={timezone}
  templateHook={templateHook}
  onNavigationAttempt={handleNavigation}
/>
```

## Protection Scenarios

### ✅ Browser Navigation
- **Tab Close**: Native browser confirmation dialog
- **Page Refresh**: Native browser confirmation dialog  
- **External Navigation**: Native browser confirmation dialog
- **Back/Forward Buttons**: Custom ConsentDialog

### ✅ Internal Navigation
- **Router Navigation**: Can be handled via `onNavigationAttempt` callback
- **Link Clicks**: Can be intercepted by parent components
- **Programmatic Navigation**: Use the provided navigation handler

## User Experience

### Browser Navigation
- Shows browser's native "Leave site?" dialog
- Standard browser messaging about unsaved changes

### Internal Navigation
- Shows custom ConsentDialog with clear messaging:
  - **Heading**: "Note Not Saved"
  - **Message**: "You have unsaved changes. Are you sure you want to leave?"
  - **Actions**: "Stay on Page" / "Leave Without Saving"

## Technical Implementation

### State Management
```typescript
const [showLeaveConfirmation, setShowLeaveConfirmation] = useState(false);
const [pendingNavigation, setPendingNavigation] = useState<(() => void) | null>(null);
```

### Event Handlers
```typescript
// Browser beforeunload protection
useEffect(() => {
  const handleBeforeUnload = (e: BeforeUnloadEvent) => {
    if (hasUnsavedChanges) {
      e.preventDefault();
      e.returnValue = '';
      return '';
    }
  };
  window.addEventListener('beforeunload', handleBeforeUnload);
  return () => window.removeEventListener('beforeunload', handleBeforeUnload);
}, [hasUnsavedChanges]);

// Browser back/forward protection
useEffect(() => {
  const handlePopState = (e: PopStateEvent) => {
    if (hasUnsavedChanges) {
      e.preventDefault();
      window.history.pushState(null, '', window.location.href);
      setPendingNavigation(() => () => window.history.back());
      setShowLeaveConfirmation(true);
    }
  };
  window.history.pushState(null, '', window.location.href);
  window.addEventListener('popstate', handlePopState);
  return () => window.removeEventListener('popstate', handlePopState);
}, [hasUnsavedChanges]);
```

## Integration with Local Storage

The navigation protection works seamlessly with the existing local storage caching:
- Changes are still cached in real-time
- Protection only warns about unsaved changes to database
- Cached data is preserved even if user navigates away

## Browser Compatibility

- **beforeunload**: Supported in all modern browsers
- **popstate**: Supported in all modern browsers  
- **ConsentDialog**: Custom component, works everywhere
- **History API**: Supported in all modern browsers

## Testing the Implementation

To test the navigation protection:

1. **Make changes** to the SOAP note
2. **Try to navigate** using:
   - Browser back button → Should show confirmation dialog
   - Tab close → Should show browser confirmation
   - Page refresh → Should show browser confirmation
   - External link → Should show browser confirmation

3. **Save the form** → All protection should be disabled

The implementation provides comprehensive protection while maintaining excellent user experience and performance.
