import { useTemplateHookReturnType } from '@/app/(dashboard)/admin/template/AddTemplate';
import { CreateAndSendPDF } from '@/app/(dashboard)/billing/invoices/CreateAndSendEmail';
/* eslint-disable react-hooks/exhaustive-deps */
import CustomDatePicker from '@/components/elements/date-picker/date-picker';
import PackageInformation from '@/components/elements/PackageInformation';
import SearchContact from '@/components/elements/search/SearchContact';
import CustomSelect from '@/components/Input/CustomSelect';
import StringInput from '@/components/Input/StringInput';
import {
  AccordionItem,
  AccordionItemContent,
  AccordionItemTrigger,
  AccordionRoot,
} from '@/components/ui/accordion';
import { Checkbox } from '@/components/ui/checkbox';
import { Field } from '@/components/ui/field';
import {
  NumberInputField,
  NumberInputRoot,
} from '@/components/ui/number-input';
import { durationOptions } from '@/data/options/duration';
import { TCreateInvoiceHook } from '@/hooks/slp/useCreateInvoiceHook';
import { Box, Flex, Icon, Stack, Text } from '@chakra-ui/react';
import { useEffect, useMemo, useState } from 'react';
import { PiWarningFill } from 'react-icons/pi';
import ButtonActions from './ButtonActions';
import NoClientPackageModal from './NoClientPackageModal';
//import TextEditor from '@/components/Input/CustomEditor';
import TextEditorNew from '@/components/Input/NewTextEditor';
import { useConfirmNavigation } from '@/hooks/slp/useConfirmNavigation';
import { ConsentDialog } from '@/components/elements/dialog/ConsentDialog';

// Local storage utility functions for soap note caching
// This implements automatic caching of soap notes to prevent data loss
// Key format: booking_slp_note -> { [bookingId]: soapNoteContent }
const SOAP_NOTE_STORAGE_KEY = 'booking_slp_note';

const getSoapNoteFromStorage = (bookingId: number | string): string | null => {
  try {
    const stored = localStorage.getItem(SOAP_NOTE_STORAGE_KEY);
    if (stored) {
      const parsedData = JSON.parse(stored);
      return parsedData[bookingId] || null;
    }
    return null;
  } catch (error) {
    console.error('Error reading soap note from localStorage:', error);
    return null;
  }
};

const saveSoapNoteToStorage = (
  bookingId: number | string,
  soapNote: string
): void => {
  try {
    const stored = localStorage.getItem(SOAP_NOTE_STORAGE_KEY);
    const existingData = stored ? JSON.parse(stored) : {};
    existingData[bookingId] = soapNote;
    localStorage.setItem(SOAP_NOTE_STORAGE_KEY, JSON.stringify(existingData));
  } catch (error) {
    console.error('Error saving soap note to localStorage:', error);
  }
};

const removeSoapNoteFromStorage = (bookingId: number | string): void => {
  try {
    const stored = localStorage.getItem(SOAP_NOTE_STORAGE_KEY);
    if (stored) {
      const existingData = JSON.parse(stored);
      delete existingData[bookingId];
      localStorage.setItem(SOAP_NOTE_STORAGE_KEY, JSON.stringify(existingData));
    }
  } catch (error) {
    console.error('Error removing soap note from localStorage:', error);
  }
};

const clearAllSoapNotesFromStorage = (): void => {
  try {
    localStorage.removeItem(SOAP_NOTE_STORAGE_KEY);
  } catch (error) {
    console.error('Error clearing all soap notes from localStorage:', error);
  }
};

export default function SoapNoteComponent({
  soapNoteHook,
  section,
  abbr,
  templateHook,
}: {
  soapNoteHook: TCreateInvoiceHook;
  section: 'slp' | 'client';
  abbr?: any;
  templateHook?: useTemplateHookReturnType;
}) {
  const {
    booking,
    values,
    // errors,
    // touched,
    // submitCount,
    setFieldValue,
    linkedClientOptions,
    setSelectedClientId,
    saveInvoiceDraft,
    // prevMemo,
    // allPackages,
    selectedClientId,
    setBooking,
    isOpenClientNoPackage,
    handleChange,
    totalDuration,
    CreateBookingLoading,
    handleFormSubmit,
    handleSearchSelect,
    handleCreateInvoiceRequestLoading,
    handleSaveInvoiceDraft,

    handleCloseNoPackageModal,
    setSearchResult,
    handleCreateAndSendInvoice,
    handleCloseSendInvoiceEmailModalAndNavigate,
    handleCloseSendInvoiceEmailModal,
    CreateAndSendInvoiceLoading,
    Slp,
    productOptions,
    searchResult,
    UpdateSlpNoteLoading,
    CreateSlpNoteLoading,
    CSEmailDisclosure,
    invoiceDataToSendAsEmail,
    isNewBooking,
    handleCreateInvoiceLoading,
    isIncompletePackageMoreThanOne,
    finalSessionOptions,
    currentIncompletePackage, // CurrentClient,
    // UpdatePackageLoading,
    UpdateBookingLoading,
    validReferralCredit,
    CurrentClient,
  } = soapNoteHook;
  // console.log('values', values);
  // console.log('saveInvoiceDraft', saveInvoiceDraft);
  const [showExperimental] = useState(false);
  const [cachedSoapNote, setCachedSoapNote] = useState<string>('');

  const hasUnsavedChanges = useMemo(() => {
    return (
      values?.soap_note !== booking?.slp_notes?.soap_note ||
      values?.invoice_memo !== booking?.slp_notes?.invoice?.memo ||
      values?.session_count !== booking?.slp_notes?.invoice?.qty
    );
  }, [values, booking]);

  const { confirmNavigation, cancelNavigation, open } =
    useConfirmNavigation(hasUnsavedChanges);

  const timezone = abbr?.zone || abbr || CurrentClient?.active_slp?.timezone;
  const selectedSessionType = finalSessionOptions.find(
    (option: any) =>
      option.value?.toLowerCase() === values?.session_type?.toLowerCase()
  );

  const isEdit = Boolean(booking?.slp_notes || booking?.slp_notes?.invoice_id);
  const isClient = section === 'client';
  const sessionIsPackage = values?.session_type?.toLowerCase() === 'package';

  // Get the booking ID for local storage operations
  const bookingId = booking?.id || values?.booking_id;

  const predictedBalance =
    currentIncompletePackage?.session_quantity -
    currentIncompletePackage?.balance +
    values?.session_count;

  const lowerBound =
    Number(currentIncompletePackage?.package_size) -
    Number(currentIncompletePackage?.balance) +
    1;
  const upperBound = lowerBound + Number(values.session_count || 0) - 1;

  const isLoading =
    UpdateSlpNoteLoading ||
    CreateBookingLoading ||
    // UpdatePackageLoading ||
    UpdateBookingLoading;
  CreateSlpNoteLoading;

  // console.log('booking is ', booking);
  // console.log('selectedClientId is ', selectedClientId);
  const replaceTags = (content: string, cb: (arg0: string) => void) => {
    const data = {
      first_name: booking?.linked_clients?.first_name,
      last_name: booking?.linked_clients?.last_name,
      invoice_date: values?.invoice_date,
    };
    const result = templateHook?.replaceTags(content, data);
    cb(result || '');
  };

  // Handle loading soap note from local storage or existing data
  useEffect(() => {
    if (bookingId) {
      // If there's already an slp note in the booking, use it and update local storage
      if (booking?.slp_notes?.soap_note) {
        const existingSoapNote = booking.slp_notes.soap_note;
        setCachedSoapNote(existingSoapNote);
        saveSoapNoteToStorage(bookingId, existingSoapNote);
        // Make sure the form field is also updated
        if (values?.soap_note !== existingSoapNote) {
          setFieldValue('soap_note', existingSoapNote);
        }
      } else {
        // If no slp_note exists, check local storage
        const storedSoapNote = getSoapNoteFromStorage(bookingId);
        // console.log('storedSoapNote', storedSoapNote);
        if (storedSoapNote) {
          setCachedSoapNote(storedSoapNote);
          setFieldValue('soap_note', storedSoapNote);
        }
      }
    }
  }, [
    bookingId,
    booking?.slp_notes?.soap_note,
    setFieldValue,
    // values?.soap_note,
  ]);

  // console.log('cachedSoapNote', cachedSoapNote);
  // console.log(' values?.soap_note', values?.soap_note);
  // Clean up cached data when the booking has been successfully saved
  useEffect(() => {
    // If the booking has slp_notes with an ID, it means it's been saved to the database
    // We can clean up the local storage cache for this booking
    if (booking?.slp_notes?.id && bookingId) {
      // Optional: Remove from cache after successful save
      // removeSoapNoteFromStorage(bookingId);
    }
  }, [booking?.slp_notes?.id, bookingId]);

  useEffect(() => {
    if (upperBound && sessionIsPackage) {
      setFieldValue(
        'invoice_memo',
        `${upperBound} / ${currentIncompletePackage?.session_quantity}`
      );
    }
  }, [
    upperBound,
    currentIncompletePackage?.session_quantity,
    setFieldValue,
    sessionIsPackage,
  ]);

  return (
    <>
      <Box minW={'50%'} flex={1}>
        <NoClientPackageModal
          isOpenClientNoPackage={isOpenClientNoPackage}
          onNoClientPackageClose={handleCloseNoPackageModal}
        />
        {(booking && booking?.clients) || isNewBooking || isClient ? (
          <form onSubmit={handleFormSubmit}>
            {' '}
            <Stack pl={'.5rem'} gap={'0.5rem'} mt={1}>
              <Text fontWeight={'semibold'} fontSize={'1.5rem'}>
                {booking?.clients?.first_name} {booking?.clients?.last_name}
              </Text>
              <Box position={'relative'}>
                <Text fontWeight={600} mb={'2px'}>
                  Session Details
                </Text>
                <Box
                  display={'flex'}
                  alignItems={'center'}
                  width={'100%'}
                  justifyContent={'space-between'}
                >
                  <CustomDatePicker
                    onChange={(e) => {
                      setFieldValue('invoice_date', e);
                    }}
                    defaultDate={values.invoice_date}
                    showTime={true}
                    timeZone={timezone}
                    isDisabled={isLoading}
                  />
                </Box>
              </Box>
              <Box position={'relative'}>
                {/* <Text fontWeight={'semibold'} fontSize={'1rem'} mb={'8px'}>
                SOAP Notes
              </Text> */}
                {linkedClientOptions?.length > 1 && (
                  <Box>
                    <CustomSelect
                      placeholder="Select one"
                      options={linkedClientOptions}
                      onChange={(val) => {
                        // if (Number(val.value) === Number(values?.client_id)) {
                        //   setSelectedClientId(null as any);
                        //   return;
                        // }
                        setFieldValue('client_id', val.value);
                        setSelectedClientId(val.value);
                        setBooking((prev: any) => ({
                          ...prev,
                          client_id: val?.value,
                          email: val?.client.emails,
                          linked_clients: val.client,
                        }));
                      }}
                      label="Linked Clients"
                      // defaultValue={linkedClientOptions?.find(
                      //   (item: any) =>
                      //     Number(item?.value) === Number(values?.client_id)
                      // )}
                      value={linkedClientOptions?.find(
                        (item: any) =>
                          Number(item?.value) ===
                          Number(selectedClientId || booking?.client_id)
                      )}
                      //value={selectedClientId || booking?.client_id}
                    />
                  </Box>
                )}

                <TextEditorNew
                  templates={templateHook?.data}
                  replaceTags={replaceTags}
                  section={'slp'}
                  // show IPA symbols
                  showIPAPicker={
                    Slp?.organization?.id === 1 ||
                    Slp?.office_title == 'Speech_Therapist'
                      ? true
                      : false
                  }
                  saveContent={(e: any) => {
                    setFieldValue('soap_note', e);
                    setCachedSoapNote(e);
                    // Save to local storage when typing
                    if (bookingId) {
                      saveSoapNoteToStorage(bookingId, e);
                    }
                  }}
                  initialContent={
                    cachedSoapNote ||
                    values?.soap_note ||
                    booking?.slp_notes?.soap_note
                  }
                  initialPresent={
                    !!booking?.slp_notes?.soap_note ||
                    !!cachedSoapNote ||
                    !!values?.soap_note
                  }
                />
              </Box>

              {Slp?.organization?.id === 1 && (
                <CustomSelect
                  placeholder="Select Session"
                  options={finalSessionOptions}
                  onChange={(val) => {
                    setFieldValue('session_type', val.value);
                  }}
                  label="Session Type"
                  selectedOption={selectedSessionType}
                />
              )}

              {Slp?.organization?.id !== 1 && (
                <CustomSelect
                  placeholder={'Select Product'}
                  options={productOptions}
                  selectedOption={productOptions?.find(
                    (item: any) =>
                      item?.value?.toLowerCase() ===
                      values.product?.toLowerCase()
                  )}
                  onChange={(val) => {
                    setFieldValue('product', val.value);
                  }}
                  label="Select Product"
                  defaultValue={productOptions?.find(
                    (item: any) => item?.value === values.product
                  )}
                />
              )}

              {isIncompletePackageMoreThanOne && (
                <Flex alignItems={'center'} gap={'1rem'}>
                  <Text fontSize={'.75rem'} color={'red'}>
                    WARNING !! There are more than one Incomplete packages{' '}
                  </Text>
                  <Icon as={PiWarningFill} boxSize={'1rem'} color={'gold'} />
                </Flex>
              )}
              {sessionIsPackage && !currentIncompletePackage && (
                <Flex alignItems={'center'} gap={'1rem'}>
                  <Text fontSize={'.75rem'} color={'red'}>
                    WARNING !! This client has no package{' '}
                  </Text>
                  <Icon as={PiWarningFill} boxSize={'1rem'} color={'gold'} />
                </Flex>
              )}
              {sessionIsPackage && currentIncompletePackage && (
                <PackageInformation
                  predictedBalance={predictedBalance}
                  data={currentIncompletePackage}
                />
              )}
              <Flex alignItems={'center'} gap={'1rem'}>
                {sessionIsPackage && currentIncompletePackage && (
                  <Field label="Session Quantity" flex={1}>
                    <NumberInputRoot
                      defaultValue={values.session_count}
                      min={1}
                      max={currentIncompletePackage?.balance}
                      value={values.session_count}
                      onValueChange={(e) =>
                        setFieldValue('session_count', Number(e.value))
                      }
                    >
                      <NumberInputField />
                    </NumberInputRoot>
                  </Field>
                )}
                {sessionIsPackage && !currentIncompletePackage && (
                  <Field label="Session Quantity" flex={1}>
                    <NumberInputRoot
                      defaultValue={values.session_count}
                      min={1}
                      value={values.session_count}
                      onValueChange={(e) =>
                        setFieldValue('session_count', Number(e.value))
                      }
                    >
                      <NumberInputField />
                    </NumberInputRoot>
                  </Field>
                )}
                {sessionIsPackage && currentIncompletePackage && (
                  <Box flex={1}>
                    <StringInput
                      inputProps={{
                        value: `${currentIncompletePackage?.session_duration} Minutes`,
                      }}
                      fieldProps={{ readOnly: true, label: 'Unit Duration' }}
                    />
                  </Box>
                )}
              </Flex>
              {sessionIsPackage && currentIncompletePackage && (
                <StringInput
                  inputProps={{
                    value: `${totalDuration} Minutes`,
                  }}
                  fieldProps={{
                    label: 'Total Duration (minutes)',
                    readOnly: true,
                  }}
                />
              )}
              {['package', 'payg', 'assessment', 'referral', ''].includes(
                String(values?.session_type)?.toLowerCase()
              ) && (
                <Stack>
                  {/* <Box>
                  <Text fontWeight={600}>Package</Text>
                  <Text>{booking.clients?.packages?.[0]?.product}</Text>
                </Box>
              
                <Box>
                  <Text fontWeight={600}>Last Session Details</Text>
                  <Text>
                    {booking.clients &&
                      booking.clients.invoices &&
                      booking.clients.invoices.sort((a: any, b: any) => {
                        return (
                          Number(new Date(b.invoice_date)) -
                          Number(new Date(a.invoice_date))
                        );
                      })[0] &&
                      `${moment(
                        booking.clients.invoices[0].invoice_date
                      ).format('MMM D, YYYY')} - ${
                        booking.clients.invoices[0].interval
                      }h x${booking.clients.invoices[0].qty} ${
                        booking.clients.invoices[0].session_type
                      } - Memo: ${prevMemo}`}
                  </Text>
                </Box> */}

                  {!sessionIsPackage && Slp?.organization?.id === 1 && (
                    <Box>
                      <CustomSelect
                        placeholder="Select Duration"
                        options={durationOptions}
                        onChange={(val) => setFieldValue('duration', val.value)}
                        label="Duration (Minutes)"
                        selectedOption={durationOptions?.find(
                          (item) => item?.value === values.duration
                        )}
                      />
                    </Box>
                  )}

                  {Slp?.organization?.id !== 1 && values.product && (
                    <StringInput
                      inputProps={{
                        value: `${values.duration} Minutes`,
                      }}
                      fieldProps={{
                        label: 'Total Duration (minutes)',
                        readOnly: true,
                      }}
                    />
                  )}

                  {sessionIsPackage && !currentIncompletePackage && (
                    <Box>
                      <CustomSelect
                        placeholder="Select duration"
                        options={durationOptions}
                        onChange={(val) => {
                          setFieldValue('duration', val.value);
                        }}
                        label="Duration (Minutes)"
                        defaultValue={durationOptions?.find(
                          (item) => item?.value === values.duration
                        )}
                      />
                    </Box>
                  )}

                  {/* {sessionIsPackage && currentIncompletePackage && (
                  <StringInput
                    inputProps={{
                      value: `${lowerBound} -  ${upperBound} / ${currentIncompletePackage?.session_quantity}`,
                    }}
                    fieldProps={{
                      label: `Suggested Memo`,
                      // isReadOnly: true,
                    }}
                  />
                )} */}

                  <StringInput
                    inputProps={{
                      placeholder: 'eg. x/12 (2023)',
                      name: 'invoice_memo',
                      value: values.invoice_memo,
                      onChange: handleChange,
                    }}
                    fieldProps={{
                      label: `Memo  (Optional)`,
                    }}
                  />
                </Stack>
              )}

              {Slp?.organization?.id === 1 && (
                <AccordionRoot borderTop={'1px solid black'} collapsible>
                  <AccordionItem
                    value={'modfication'}
                    boxShadow={'none'}
                    alignItems={'center'}
                  >
                    <AccordionItemTrigger
                      p={'2'}
                      borderRadius="lg"
                      _hover={{
                        bg: 'gray.50',
                      }}
                      width={'100%'}
                      cursor={'pointer'}
                    >
                      Add Modifications
                    </AccordionItemTrigger>
                    <AccordionItemContent>
                      <Box>
                        <Stack gap={'1rem'} pl={2} mt={2}>
                          {['package', 'assessment'].includes(
                            values?.session_type?.toLowerCase()
                          ) && (
                            <Checkbox
                              checked={values.no_show === 'true'}
                              onCheckedChange={(e) => {
                                setFieldValue(
                                  'no_show',
                                  e.checked ? 'true' : 'false'
                                );
                              }}
                              alignItems={'flex-start'}
                              colorScheme="primary"
                            >
                              <Text mb={'.2rem'} fontWeight={600}>
                                No Show
                              </Text>
                              <Text color={'gray.200'}>
                                Late cancellation or no show
                              </Text>
                            </Checkbox>
                          )}

                          {/* {values.session_type === 'referral' && ( */}

                          {values?.session_type?.toLowerCase() ===
                            'assessment' && (
                            <Checkbox
                              checked={values.split_ax === 'true'}
                              onCheckedChange={(e) => {
                                setFieldValue(
                                  'split_ax',
                                  e.checked ? 'true' : 'false'
                                );
                              }}
                              alignItems={'flex-start'}
                              colorScheme="primary"
                            >
                              <Text mb={'.2rem'} fontWeight={600}>
                                Split Assessment
                              </Text>
                              <Text color={'gray.200'}>
                                Split ax into two separate invoices for
                                insurance purposes
                              </Text>
                            </Checkbox>
                          )}
                          {values?.session_type?.toLowerCase() === 'payg' && (
                            <Checkbox
                              checked={values.referral === 'true'}
                              onCheckedChange={(e) => {
                                setFieldValue(
                                  'referral',
                                  e.checked ? 'true' : 'false'
                                );
                              }}
                              alignItems={'flex-start'}
                              colorScheme="primary"
                            >
                              <Text mb={'.2rem'} fontWeight={600}>
                                Free Referral Session (Credit:{' '}
                                {validReferralCredit})
                              </Text>
                              <Text color={'gray.200'}>
                                Free session from a referral or by referring
                              </Text>
                            </Checkbox>
                          )}
                          {/* )} */}
                        </Stack>
                      </Box>
                    </AccordionItemContent>
                  </AccordionItem>
                </AccordionRoot>
              )}

              {CSEmailDisclosure.open && (
                <CreateAndSendPDF
                  isInvoiceLoading={
                    CreateAndSendInvoiceLoading ||
                    CreateSlpNoteLoading ||
                    CreateBookingLoading ||
                    UpdateBookingLoading
                  }
                  isOpen={CSEmailDisclosure.open}
                  onClose={handleCloseSendInvoiceEmailModalAndNavigate}
                  invoice={invoiceDataToSendAsEmail}
                  onCloseModal={handleCloseSendInvoiceEmailModal}
                />
              )}
              <Flex
                alignItems="center"
                mt="1rem"
                gap="1rem"
                mb={{ base: '1rem', md: '0.5rem' }}
              >
                {/* Submit Button */}

                <ButtonActions
                  Slp={Slp}
                  isEdit={isEdit}
                  handleCreateInvoiceRequestLoading={
                    handleCreateInvoiceRequestLoading
                  }
                  handleFormSubmit={handleFormSubmit}
                  handleCreateInvoiceLoading={handleCreateInvoiceLoading}
                  saveInvoiceDraft={saveInvoiceDraft}
                  handleSaveInvoiceDraft={handleSaveInvoiceDraft}
                  handleCreateAndSendInvoice={handleCreateAndSendInvoice}
                />

                {/* Dropdown Menu */}
                {/* <Box position="relative">
                <MenuRoot>
                  <MenuTrigger cursor="pointer" outline={'none'}>
                    <Center
                      h={{ base: '30px', md: '40px' }}
                      w={{ base: '30px', md: '40px' }}
                      bg="#F5F8F7"
                      rounded="full"
                      mb={'5px'}
                    >
                      <BsThreeDotsVertical size="18px" color="#111413" />
                    </Center>
                  </MenuTrigger>

                  <MenuContent
                    position="absolute"
                    top="100%" // Place the dropdown below the trigger
                    left="0"
                    zIndex="10" // Ensure it appears above other elements
                    bg="#FFF"
                    boxShadow="0px 4px 6px rgba(0, 0, 0, 0.1)" // Optional shadow for better visibility
                  >
                    <MenuItem
                      value="edit"
                      cursor="pointer"
                      _hover={{ bg: 'transparent' }}
                    >
                      <Text
                        onClick={handleCreateAndSendInvoice}
                        fontWeight={400}
                        whiteSpace="nowrap" // Prevents text wrapping
                      >
                        {isEdit
                          ? 'Save Edits (experimental)'
                          : 'Save and send invoice (experimental)'}
                      </Text>
                    </MenuItem>
                  </MenuContent>
                </MenuRoot>
              </Box> */}
              </Flex>
              {showExperimental && (
                <Flex alignItems={'center '} gap={'1rem'}>
                  {/* <Button
                  backgroundColor={'#FFA500'}
                  _hover={{ backgroundColor: '#e69500' }}
                  loading={handleCreateInvoiceLoading}
                  onClick={handleCreateAndSendInvoice}
                  disabled={handleCreateInvoiceLoading}
                >
                  {isEdit ? 'Save Edits' : 'Save (experimental)'}
                </Button> */}

                  {/* 
                {Boolean(booking?.slp_notes?.invoice_id) && (
                  <Button
                    bg={'transparent !important'}
                    color={'primary.500'}
                    variant={'subtle'}
                    borderColor={'primary.500'}
                    border={'1px solid'}
                  >
                    Send Invoice (Experimental)
                  </Button>
                )} */}
                  {/* <MenuRoot>
                  <MenuTrigger
                    // as={Button}
                    bg="transparent"
                    _focus={{ bg: 'transparent', boxShadow: 'none' }}
                    _hover={{ bg: 'transparent', cursor: 'pointer' }}
                    _active={{ bg: 'transparent' }}
                  >
                    <Center
                      h={{ base: '30px', md: '40px' }}
                      w={{ base: '30px', md: '40px' }}
                      bg="#F5F8F7"
                      rounded={'full'}
                    >
                      <BsThreeDotsVertical size="18px" color="#111413" />
                    </Center>
                  </MenuTrigger>
                  <MenuContent bg="#E0E6E5" border="none" position={'absolute'}>
                    <MenuItem
                      bg="#E0E6E5"
                      _hover={{ bg: 'transparent' }}
                      value={'edit'}
                      cursor={'pointer'}
                    >
                      <Button
                        backgroundColor={'#FFA500'}
                        _hover={{ backgroundColor: '#e69500' }}
                        loading={handleCreateInvoiceLoading}
                        onClick={handleCreateAndSendInvoice}
                        disabled={handleCreateInvoiceLoading}
                      >
                        {isEdit
                          ? 'Save Edits'
                          : 'Save and send invoice (experimental)'}
                      </Button>
                    </MenuItem>
                  </MenuContent>
                </MenuRoot> */}
                </Flex>
              )}
            </Stack>
          </form>
        ) : (
          // New Session Modal
          <Stack>
            <label className="font-medium text-gray-900">Lookup Client</label>
            <SearchContact
              setSearchResult={(e: any) => {
                setSearchResult(e);
              }}
              searchResult={searchResult}
              selectExistingUser={(item) => handleSearchSelect(item)}
            />
          </Stack>
        )}
      </Box>
      <ConsentDialog
        open={open}
        onOpenChange={cancelNavigation}
        handleSubmit={confirmNavigation}
        note={`You have unsaved changes. Are you sure you want to leave?`}
        isLoading={CreateAndSendInvoiceLoading}
        heading={'Note Not Saved'}
      />
    </>
  );
}

// Export utility functions for use in other components
export {
  clearAllSoapNotesFromStorage,
  getSoapNoteFromStorage,
  removeSoapNoteFromStorage,
  saveSoapNoteToStorage,
};
