'use client';

import { useGetOrganizationBySlugQuery } from '@/api/organizations/get-by-slug';
import soapLogo from '@/assets/soapLogo.png';
import Logo from '@/components/elements/logo/Logo';
import {
  Box,
  Center,
  Container,
  Heading,
  Stack,
  Text,
  VStack,
} from '@chakra-ui/react';
import Link from 'next/link';
import { IoArrowBack, IoMailUnreadOutline } from 'react-icons/io5';

export default function Page({
  params,
}: {
  params: { organization_name: string };
}) {
  const { data: OrganizationData } = useGetOrganizationBySlugQuery(
    {
      slug: params.organization_name,
      isPublic: 'true',
    },
    { enable: Boolean(params.organization_name) }
  );

  return (
    <div>
      <Container maxW="lg" py={{ base: '12', md: '24' }}>
        <Stack gap="4">
          <VStack>
            <Logo src={OrganizationData?.[0]?.logo_url || soapLogo?.src} />
          </VStack>
          <Box
            border="1px solid"
            borderColor={'#e5e7eb'}
            //shadow={'sm'}
            rounded={'lg'}
            pb={{ base: '0', md: '5' }}
            pt={'7'}
            px={{ base: '3', md: '5' }}
          >
            <VStack gap={'1.5'} mb={'10'}>
              <Center
                color={'green.600'}
                bg={'green.100'}
                w={'16'}
                h={'16'}
                rounded={'full'}
              >
                <IoMailUnreadOutline size={28} />
              </Center>
              <Heading
                textAlign={'center'}
                fontSize="22px"
                mt={{ base: '3', md: '6' }}
                color="#111827"
              >
                Check Your Email
              </Heading>
              <Text
                color="#374151"
                fontSize={{ base: 'sm', md: 'md' }}
                textAlign={'center'}
                maxW={{ base: '90%', md: '80%' }}
              >
                Email sent - if an account exists with this email, you will
                receive an email with a link to login.
              </Text>
            </VStack>
            <Link href={`/client/${params.organization_name}/login`}>
              <Center
                mb={'1rem'}
                fontWeight={500}
                fontSize={{ base: 'sm', md: 'md' }}
                color="#374151"
                border="1px solid"
                rounded={'md'}
                py={'3'}
                gap={'2'}
                borderColor={'#d1d5db'}
                _hover={{ bg: '#f9fafb', color: 'black' }}
              >
                <IoArrowBack size={18} />
                Go Back To Login
              </Center>
            </Link>
          </Box>
        </Stack>
      </Container>
    </div>
  );
}
