import {
  <PERSON>u<PERSON>ontent,
  MenuItem,
  MenuRoot,
  // MenuSeparator,
  MenuTrigger,
} from '@/components/ui/menu';
import { toaster } from '@/components/ui/toaster';
import 'moment-timezone';
import 'moment/locale/en-ca';
import moment from 'moment/moment';
import { useMemo, useState } from 'react';
import { BsThreeDotsVertical } from 'react-icons/bs';

import { formatMoney } from '@/components/elements/format-money/FormatMoney';
import { generateAndDownloadPDF } from '@/components/elements/pdf/Generate-PDF';
import Link from 'next/link';
import { Box } from '@chakra-ui/react';
import { Spinner } from '@chakra-ui/react';

export default function RowActions({
  invoice,
  organization_name,
}: {
  invoice: any;
  color?: any;
  organization_name: any;
}) {
  const { transactions, total_price } = invoice ?? {};
  const [isGeneratingLink, setIsGeneratingLink] = useState(false);

  // console.log('invoice', invoice);

  const totalPaid = transactions?.reduce(
    (prev: number, currentTransaction: any) => {
      return prev + Number(currentTransaction?.amount || 0);
    },
    0
  );
  const amountDue = Number(total_price) - Number(totalPaid);
  const resolvedAmountDue = useMemo(() => {
    if (amountDue < 0) {
      return `(${formatMoney(Math.abs(amountDue), {
        currencyCode: invoice?.currency_code,
      })})`;
    }
    return formatMoney(amountDue, {
      currencyCode: invoice?.currency_code,
    });
  }, [amountDue, invoice?.currency_code]);

  const handleDownload = async () => {
    try {
      await generateAndDownloadPDF({
        name: String(invoice?.clients?.display_name || invoice?.name),
        receiptNumber: String(invoice?.invoice_number),
        date: String(moment(invoice?.invoice_date).format('YYYY-MM-DD')),
        activity: invoice?.product ? String(invoice?.product) : '',
        quantity: Number(invoice?.qty),
        rate: Number(invoice?.total_price),
        balance: 0,
        memo: String(invoice?.memo || ''),
        referral: invoice?.referral,
        amountDue: amountDue,
        dueDate: invoice?.due_date,
        email: invoice?.email,
        transactions,
        invoice,
        resolvedAmountDue,
      });
      // toaster.create({
      //   type: 'success',
      //   description: `PDF successfully downloaded`,
      // });
    } catch (error) {
      toaster.create({
        type: 'error',
        description: `Failed to download PDF: ${error}`,
      });
    }
  };

  const handlePaymentLink = async (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent menu from closing
    setIsGeneratingLink(true);
    try {
      const response = await fetch('/api/stripe/invoice-payment-link', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          invoiceId: invoice.id,
          user_id: invoice.slp_id,
          organization_name,
        }),
      });

      if (!response.ok) {
        throw new Error(
          (await response.json())?.message || 'Failed to generate link'
        );
      }

      const { link } = await response.json();

      window.open(link, '_blank');
    } catch (error: any) {
      toaster.create({
        description: error.message || 'Operation Failed',
        type: 'error',
      });
    } finally {
      setIsGeneratingLink(false);
    }
  };

  return (
    <div>
      <MenuRoot positioning={{ placement: 'bottom' }}>
        <MenuTrigger outline={'none !important'} cursor={'pointer'}>
          <Box cursor={'pointer'} className="cursor-pointer">
            {isGeneratingLink ? <Spinner size="sm" /> : <BsThreeDotsVertical />}
          </Box>{' '}
        </MenuTrigger>
        <MenuContent cursor={'pointer'}>
          <MenuItem
            cursor={'pointer'}
            value="generatePDF"
            onClick={handleDownload}
          >
            Generate PDF
          </MenuItem>
          <MenuItem
            onClick={handlePaymentLink}
            disabled={isGeneratingLink}
            value="g-pay-link"
          >
            Make Payment
          </MenuItem>
          <MenuItem cursor={'pointer'} value="view">
            <Link href={`/client/invoice/${invoice?.id}`}>View</Link>
          </MenuItem>
        </MenuContent>
      </MenuRoot>
    </div>
  );
}
