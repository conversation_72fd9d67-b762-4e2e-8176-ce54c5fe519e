import StringInput from '@/components/Input/StringInput';
import { Box, Button, Flex, Text } from '@chakra-ui/react';
const AddEmailModal = ({
  onClose,
  emailHook,
  clientData,
  // refetch,
}: {
  onClose: any;
  emailHook: any;
  clientData: any;
  // refetch: any;
}) => {
  const { lookupEmail, lookupEmailLoading, onInputChange, addNewEmail } =
    emailHook;

  // console.log('lookupEmail', lookupEmail);

  const handleAddEmail = async () => {
    if (!clientData?.client?.id) {
      console.error('No client ID available');
      return;
    }

    await addNewEmail(clientData?.client?.id);
  };

  return (
    <Box>
      <Text mt={'2rem'} textAlign={'center'} fontWeight={600} fontSize={'1rem'}>
        Add New Email
      </Text>

      <StringInput
        inputProps={{
          value: lookupEmail,
          onChange: onInputChange,
          placeholder: 'Enter email address',
        }}
        fieldProps={{ label: 'New Email Address' }}
      />

      <Flex justifyContent={'flex-end'} gap={2}>
        <Button variant={'outline'} mt={'1rem'} onClick={onClose} minW={'8rem'}>
          Cancel
        </Button>
        <Button
          bg={'primary.500'}
          color={'white'}
          mt={'1rem'}
          onClick={handleAddEmail}
          loading={lookupEmailLoading}
          minW={'8rem'}
          disabled={!lookupEmail}
        >
          {lookupEmailLoading ? 'Adding...' : 'Add Email'}
        </Button>
      </Flex>
    </Box>
  );
};

export default AddEmailModal;
