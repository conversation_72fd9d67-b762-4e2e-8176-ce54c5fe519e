import { toaster } from '@/components/ui/toaster';
import { ToastMessages } from '@/constants/toast-messages';
import {
  ExtractFnReturnType,
  MutationConfig,
  QueryConfigType,
  useMutation,
  useQuery,
} from '@/lib/react-query';

const updatePrimaryEmailFalse = async ({ id, ...body }: any) => {
  const response = await fetch(`/api/public/client-emails/update-email-false`, {
    method: 'PUT',
    body: JSON.stringify({ id, ...body }),
  });
  if (!response.ok) throw new Error('Error updating primary email false');
  return response.json();
};
type UpdateQueryFnType = typeof updatePrimaryEmailFalse;

export const useUpdatePrimaryEmailFalseMutation = (
  config?: MutationConfig<UpdateQueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    retry: false,
    mutationKey: ['update-primary-email-false'],
    mutationFn: updatePrimaryEmailFalse,
    ...config,
  });
};

const updatePrimaryEmailTrue = async ({ clientId, email, ...body }: any) => {
  //   console.log('body', body);
  const response = await fetch(`/api/public/client-emails/update-email-true`, {
    method: 'PUT',
    body: JSON.stringify({ clientId, email, ...body }),
  });
  if (!response.ok) throw new Error('Error updating primary email false');
  return response.json();
};
type UpdateQueryFnTypeTwo = typeof updatePrimaryEmailTrue;

export const useUpdatePrimaryEmailTrueMutation = (
  config?: MutationConfig<UpdateQueryFnTypeTwo>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    retry: false,
    mutationKey: ['update-primary-email-true'],
    mutationFn: updatePrimaryEmailTrue,
    ...config,
  });
};

const addNewEmail = async (body: any) => {
  console.log('body', body);
  const response = await fetch(`/api/public/client-emails/add-email`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) throw new Error('Error creating primary email false');
  return response.json();
};

type UpdateQueryFnTypeThree = typeof addNewEmail;

export const useAddNewEmailMutation = (
  config?: MutationConfig<UpdateQueryFnTypeThree>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    retry: false,
    mutationKey: ['add-new-email'],
    mutationFn: addNewEmail,
    ...config,
  });
};

const createActivity = async (body: any) => {
  console.log('body', body);
  const response = await fetch(`/api/public/client-emails/create-activity`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) throw new Error('Error creating primary email false');
  return response.json();
};

type UpdateQueryFnTypeFour = typeof createActivity;

export const useAddClientActivityMutation = (
  config?: MutationConfig<UpdateQueryFnTypeFour>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    retry: false,
    mutationKey: ['add-new-activity'],
    mutationFn: createActivity,
    ...config,
  });
};
export async function getClientInvoiceById(id: any) {
  const response = await fetch(
    `/api/public/clients/get-invoice-id/${Number(id)}`,
    {
      method: 'GET',
      cache: 'no-store',
    }
  );

  const json = await response.json();
  return json;
}

type QueryFnType = typeof getClientInvoiceById;

export const useGetClientInvoiceByIdCM = (
  id: any,
  config?: QueryConfigType<QueryFnType>
) => {
  // const { id } = data;

  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: ['get-client-invoice-by-id', id],
    queryFn: () => getClientInvoiceById(id),
    ...config,
  });
};

export async function getNewSfClientInvoices(id: any, organization_id: any) {
  const response = await fetch(
    `/api/public/clients/newsf/${Number(id)}/${organization_id}`,
    {
      method: 'GET',
      cache: 'no-store',
    }
  );

  const json = await response.json();
  return json;
}
type QueryFnTypeSix = typeof getNewSfClientInvoices;

export const useGetNewSfClientInvoicesQueryCM = (
  data: {
    id: string;
    organization_id?: string;
  },
  config?: QueryConfigType<QueryFnTypeSix>
) => {
  // const { id } = data;
  const { id, organization_id } = data;
  return useQuery<ExtractFnReturnType<QueryFnTypeSix>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: ['get-new-sf-client-invoices', id, organization_id],
    queryFn: () => getNewSfClientInvoices(id, organization_id),
    ...config,
  });
};
export async function getClientOrgById(organization_id: number) {
  const response = await fetch(`/api/public/organizations/${organization_id}`, {
    method: 'GET',
    cache: 'no-store',
  });

  const json = await response.json();
  return json;
}
type QueryFnTypeSeven = typeof getClientOrgById;

export const useGetClientOrgByIdQueryCM = (
  organization_id: number,
  config?: QueryConfigType<QueryFnTypeSeven>
) => {
  // const { id } = data;
  return useQuery<ExtractFnReturnType<QueryFnTypeSeven>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: ['get-client-org-by-id', organization_id],
    queryFn: () => getClientOrgById(organization_id),
    ...config,
  });
};
