'use client';

import AddressAutocomplete from '@/components/elements/AddressAutocomplete';
import GoogleMapsScript from '@/components/elements/GoogleMapsScript';
import { CustomModal } from '@/components/elements/modal/custom-modal';
import CustomSelect from '@/components/Input/CustomSelect';
import StringInput from '@/components/Input/StringInput';
import { Button } from '@/components/ui/button';
import { toaster } from '@/components/ui/toaster';
import { useCountryStateCityHook } from '@/hooks/countryStateCity/useCountryStateCityHook';
import {
  Box,
  Card,
  Center,
  Flex,
  Separator,
  SimpleGrid,
  Text,
  VStack,
} from '@chakra-ui/react';
import { useCallback, useEffect, useState } from 'react';
import AddEmailModal from './AddEmailModal';
import { useAddClientActivityMutation } from './query';
import { useEmailHook } from './useEmailHook';

interface ClientData {
  id: number;
  email: string;
  organization_id: number;
  client_id: number;
  client: {
    id: number;
    first_name: string;
    last_name: string;
    middle_name?: string;
    display_name?: string;
    address?: string;
    province?: string;
    dob?: string;
    postal_code?: string;
    phone?: string;
    country?: any;
    state?: any;
    city?: any;
  };
  client_emails?: {
    id: number;
    email: string;
    is_primary_email: boolean;
  }[];
}

// Province options - you might want to move this to a separate file
// const provinceOptions = [
//   { label: 'Alberta', value: 'Alberta' },
//   { label: 'British Columbia', value: 'British Columbia' },
//   { label: 'Manitoba', value: 'Manitoba' },
//   { label: 'New Brunswick', value: 'New Brunswick' },
//   { label: 'Newfoundland and Labrador', value: 'Newfoundland and Labrador' },
//   { label: 'Northwest Territories', value: 'Northwest Territories' },
//   { label: 'Nova Scotia', value: 'Nova Scotia' },
//   { label: 'Nunavut', value: 'Nunavut' },
//   { label: 'Ontario', value: 'Ontario' },
//   { label: 'Prince Edward Island', value: 'Prince Edward Island' },
//   { label: 'Quebec', value: 'Quebec' },
//   { label: 'Saskatchewan', value: 'Saskatchewan' },
//   { label: 'Yukon', value: 'Yukon' },
//   { label: 'Other', value: 'Other' },
// ];

const ProfileContent = ({
  clientData,
  refetch,
}: {
  clientData: ClientData | null;
  refetch: () => void;
}) => {
  // console.log('clientData', clientData);
  const emailHook = useEmailHook({ clientData, refetch });
  const { mutateAsync: createClientActivity } = useAddClientActivityMutation();

  const {
    setPrimaryEmail,
    setPrimaryLoading,
    hasMultiplePrimaryEmail,
    removeAsPrimary,
    addEmailDisclosure,
  } = emailHook;

  interface LocationOption {
    name: string;
    isoCode?: string;
    [key: string]: any;
  }

  interface FormDataType {
    firstName: string;
    lastName: string;
    middleName: string;
    displayName: string;
    address: string;
    province: string;
    dob: string;
    postalCode: string;
    phone: string;
    country: LocationOption | null;
    state: LocationOption | null;
    city: LocationOption | null;
  }

  const [formData, setFormData] = useState<FormDataType>({
    firstName: '',
    lastName: '',
    middleName: '',
    displayName: '',
    address: '',
    province: '',
    dob: '',
    postalCode: '',
    phone: '',
    country: null,
    state: null,
    city: null,
  });

  const [isUserEditingDisplayName, setIsUserEditingDisplayName] =
    useState('rendered');
  const [displayNameError, setDisplayNameError] = useState(false);

  // Country, state, city hook
  const { countryOptions, stateOptions, cityOptions } = useCountryStateCityHook(
    {
      countryCode: formData?.country?.isoCode ?? '',
      stateCode: formData?.state?.isoCode ?? '',
    }
  );

  useEffect(() => {
    if (clientData?.client) {
      setFormData({
        firstName: clientData?.client?.first_name ?? '',
        lastName: clientData?.client?.last_name ?? '',
        middleName: clientData?.client?.middle_name ?? '',
        displayName: clientData?.client?.display_name ?? '',
        address: clientData?.client?.address ?? '',
        province: clientData?.client?.province ?? '',
        dob: clientData?.client?.dob
          ? clientData?.client?.dob.split('T')[0]
          : '',
        postalCode: clientData?.client?.postal_code ?? '',
        phone: clientData?.client?.phone ?? '',
        country: clientData?.client?.country ?? null,
        state: clientData?.client?.state ?? null,
        city: clientData?.client?.city ?? null,
      });
    }
  }, [clientData?.client]);

  const [isLoading, setIsLoading] = useState(false);

  const validateSelection = useCallback((value: string) => {
    const [firstName, lastName] = value.trim().split(' ');
    return !(firstName && lastName);
  }, []);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Handle display name auto-generation logic
    if (field === 'firstName' || field === 'lastName') {
      if (
        isUserEditingDisplayName === 'false' ||
        isUserEditingDisplayName === 'rendered'
      ) {
        const firstName = field === 'firstName' ? value : formData.firstName;
        const lastName = field === 'lastName' ? value : formData.lastName;
        const autoGeneratedDisplayName =
          `${firstName.trim()} ${lastName.trim()}`.trim();

        if (autoGeneratedDisplayName) {
          setFormData((prev) => ({
            ...prev,
            displayName: autoGeneratedDisplayName,
          }));
          setDisplayNameError(validateSelection(autoGeneratedDisplayName));
        }
      }
      setIsUserEditingDisplayName('false');
    }

    if (field === 'displayName') {
      setIsUserEditingDisplayName('true');
      setDisplayNameError(validateSelection(value));
    }
  };

  const handleSelectChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleAddressSelect = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      address: value,
    }));
  };

  const handleSave = async () => {
    if (!clientData) return;

    if (displayNameError) {
      toaster.create({
        type: 'error',
        description: 'First name and last name are required for display name.',
      });
      return;
    }

    setIsLoading(true);

    try {
      console.log('clientData', clientData);
      console.log('formData', formData);
      const response = await fetch(`/api/public/clients/profile`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName: formData.firstName,
          lastName: formData.lastName,
          middleName: formData.middleName,
          displayName: formData.displayName,
          address: formData.address,
          // province: formData.province,
          dob: formData.dob,
          postalCode: formData.postalCode,
          phone: formData.phone,
          country: formData.country,
          state: formData.state,
          city: formData.city,
          clientId: clientData.client_id,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update profile');
      }

      const activityInsertData = {
        client_id: clientData?.client?.id,
        activity_type: 'profile_updated',
        activity_date: new Date().toISOString(),
        details: {
          type: 'data_updated',
        },
        organization_id: clientData?.organization_id,
      };

      await createClientActivity(activityInsertData);
      await refetch();
      toaster.create({
        type: 'success',
        description: 'Profile updated successfully!',
      });
    } catch (error) {
      console.error('Error updating profile:', error);
      toaster.create({
        type: 'error',
        description: 'Failed to update profile. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <GoogleMapsScript />
      <Box width={'100%'} spaceY={3}>
        <Card.Root borderColor={'gray.50'}>
          <Card.Header gap={'0.5'} px={{ base: '3', md: '6' }}>
            <Card.Title fontSize={{ base: 'lg', lg: 'xl' }}>
              Personal Information
            </Card.Title>
            <Card.Description
              color="#6b7280"
              fontSize={{ base: 'sm', lg: 'md' }}
            >
              Update your personal details and contact information
            </Card.Description>
          </Card.Header>
          <Card.Body px={{ base: '3', md: '6' }}>
            <VStack gap={6} align="stretch">
              {/* Name Fields */}
              <SimpleGrid gap={'2rem'} columns={{ base: 1, md: 3 }}>
                <Box>
                  <StringInput
                    inputProps={{
                      borderColor: 'gray.100',
                      name: 'first_name',
                      value: formData.firstName,
                      onChange: (e) =>
                        handleInputChange('firstName', e.target.value),
                    }}
                    fieldProps={{ label: 'First Name' }}
                  />
                </Box>
                <Box>
                  <StringInput
                    inputProps={{
                      borderColor: 'gray.100',
                      name: 'middle_name',
                      value: formData.middleName,
                      onChange: (e) =>
                        handleInputChange('middleName', e.target.value),
                    }}
                    fieldProps={{ label: 'Middle Name' }}
                  />
                </Box>
                <Box>
                  <StringInput
                    inputProps={{
                      borderColor: 'gray.100',
                      name: 'last_name',
                      value: formData.lastName,
                      onChange: (e) =>
                        handleInputChange('lastName', e.target.value),
                    }}
                    fieldProps={{ label: 'Last Name' }}
                  />
                </Box>
              </SimpleGrid>

              {/* Display Name */}
              <Box>
                <StringInput
                  inputProps={{
                    borderColor: 'gray.100',
                    name: 'display_name',
                    value: formData.displayName,
                    onChange: (e) =>
                      handleInputChange('displayName', e.target.value),
                  }}
                  fieldProps={{
                    label: 'Customer Display Name',
                    errorText: displayNameError
                      ? 'First name and last name are required.'
                      : undefined,
                  }}
                />
              </Box>

              {/* Personal Information */}
              <SimpleGrid gap={'2rem'} columns={{ base: 1, md: 2 }}>
                <Box>
                  <StringInput
                    inputProps={{
                      borderColor: 'gray.100',
                      name: 'phone',
                      value: formData.phone,
                      onChange: (e) =>
                        handleInputChange('phone', e.target.value),
                    }}
                    fieldProps={{ label: 'Phone' }}
                  />
                </Box>
                <Box>
                  <StringInput
                    inputProps={{
                      borderColor: 'gray.100',
                      name: 'dob',
                      type: 'date',
                      value: formData.dob,
                      onChange: (e) => handleInputChange('dob', e.target.value),
                    }}
                    fieldProps={{ label: 'Date of Birth' }}
                  />
                </Box>
              </SimpleGrid>

              <Box>
                <Separator />
              </Box>

              {/* Address Information */}
              <Text fontSize={'lg'} fontWeight={'600'}>
                Address Information
              </Text>

              <SimpleGrid gap={'2rem'} columns={{ base: 1, md: 2 }}>
                <AddressAutocomplete
                  value={formData.address}
                  onSelect={handleAddressSelect}
                  onChange={(e: any) =>
                    handleInputChange('address', e.target.value)
                  }
                />
                <Box>
                  <StringInput
                    inputProps={{
                      borderColor: 'gray.100',
                      name: 'postal_code',
                      value: formData.postalCode,
                      onChange: (e: any) =>
                        handleInputChange('postalCode', e.target.value),
                    }}
                    fieldProps={{ label: 'Postal Code' }}
                  />
                </Box>
              </SimpleGrid>

              <SimpleGrid gap={'2rem'} columns={{ base: 1, md: 3 }}>
                <Box>
                  <CustomSelect
                    onChange={(option) => {
                      handleSelectChange('country', option.value);
                    }}
                    selectedOption={countryOptions?.find(
                      (option) =>
                        formData?.country &&
                        option?.value?.isoCode === formData?.country?.isoCode
                    )}
                    options={countryOptions || []}
                    label="Country"
                  />
                </Box>
                <Box>
                  <CustomSelect
                    onChange={(option) => {
                      handleSelectChange('state', option.value);
                    }}
                    selectedOption={stateOptions?.find(
                      (option) =>
                        formData?.state &&
                        option?.value?.isoCode === formData?.state?.isoCode
                    )}
                    options={stateOptions || []}
                    label="State/Province"
                  />
                </Box>
                <Box>
                  <CustomSelect
                    onChange={(option) => {
                      handleSelectChange('city', option.value);
                    }}
                    selectedOption={cityOptions?.find(
                      (option) =>
                        formData?.city &&
                        option?.value?.name === formData?.city?.name
                    )}
                    options={cityOptions || []}
                    label="City"
                  />
                </Box>
              </SimpleGrid>

              {/* Legacy Province Field (if still needed) */}
              {/* <Box>
                <CustomSelect
                  controlStyle={{
                    border: '1px solid #a2a5ab',
                    borderRadius: '0.375rem',
                  }}
                  placeholder="Select province"
                  options={provinceOptions}
                  onChange={(val) => {
                    handleSelectChange('province', val.value);
                  }}
                  label="Province (Legacy)"
                  defaultValue={provinceOptions.find(
                    (item) =>
                      formData?.province &&
                      item.value.toLowerCase() ===
                        formData?.province?.toLowerCase()
                  )}
                />
              </Box> */}

              {/* Email Field (Read-only) */}
              {/* <Field.Root>
                <Field.Label color={'#111827'} fontWeight={'600'}>
                  Email
                </Field.Label>
                <Input
                  type="email"
                  value={clientData?.email ?? ''}
                  borderColor={'gray.50'}
                  readOnly
                  bg={'gray.50'}
                  cursor={'not-allowed'}
                />
              </Field.Root> */}

              <Button
                alignSelf="start"
                bg={'#e97a5b'}
                _hover={{ bg: 'orange.600' }}
                onClick={handleSave}
                disabled={isLoading}
              >
                {isLoading ? 'Saving...' : 'Save Changes'}
              </Button>
            </VStack>
          </Card.Body>
        </Card.Root>

        {/* Email Addresses Section */}
        <Card.Root borderColor={'gray.50'}>
          <Card.Header gap={'0.5'} px={{ base: '3', md: '6' }}>
            <Card.Title fontSize={{ base: 'lg', lg: 'xl' }}>
              Email Addresses
            </Card.Title>
            <Card.Description
              color="#6b7280"
              fontSize={{ base: 'sm', lg: 'md' }}
            >
              Manage your email addresses and set a primary email
            </Card.Description>
          </Card.Header>
          <Card.Body px={{ base: '3', md: '6' }}>
            <VStack gap={4} align="stretch">
              <div>
                {clientData?.client_emails &&
                  clientData?.client_emails
                    .sort(
                      (a: any, b: any) =>
                        b.is_primary_email - a.is_primary_email
                    )
                    .map((email) => {
                      return (
                        <Flex
                          mt={2}
                          alignItems={'center'}
                          gap={4}
                          key={email.id}
                        >
                          <StringInput
                            inputProps={{
                              id: 'email',
                              name: 'email',
                              type: 'email',
                              readOnly: true,
                              value: email.email,
                              disabled: true,
                              w: { base: 'fit-content', md: '24rem' },
                            }}
                          />

                          {email.is_primary_email ? (
                            <Flex gap={4} alignItems={'center'}>
                              <Center
                                rounded={'full'}
                                bg={'green.50'}
                                fontWeight={'medium'}
                                w={{ base: 'full', md: '10rem' }}
                                border={'1px solid rgba(22,163,74,0.2)'}
                                color={'green.700'}
                                py={'.3rem'}
                              >
                                Primary
                              </Center>

                              {hasMultiplePrimaryEmail && (
                                <Button
                                  bg={'gray'}
                                  minW={{ base: 'fit-content', md: '10rem' }}
                                  _hover={{ bg: 'gray' }}
                                  onClick={() =>
                                    removeAsPrimary(
                                      Number(clientData?.id),
                                      email.email
                                    )
                                  }
                                >
                                  Remove as primary
                                </Button>
                              )}
                            </Flex>
                          ) : (
                            <Button
                              onClick={() =>
                                setPrimaryEmail(
                                  Number(clientData?.client?.id),
                                  email.email
                                )
                              }
                              loading={
                                email.email === setPrimaryLoading.email &&
                                setPrimaryLoading.loading
                              }
                              minW={{ base: 'fit-content', md: '10rem' }}
                              bg={'primary.500'}
                            >
                              Set as Primary
                            </Button>
                          )}
                        </Flex>
                      );
                    })}

                <Box mt={'1rem'} display={'flex'} gap={2}>
                  <Button
                    onClick={addEmailDisclosure.onOpen}
                    minW={{ base: 'fit-content', md: '10rem' }}
                  >
                    Add Email
                  </Button>
                </Box>

                <CustomModal
                  w={{ base: '90%', md: '40rem' }}
                  open={addEmailDisclosure.open}
                  onOpenChange={addEmailDisclosure.onClose}
                >
                  <AddEmailModal
                    onClose={addEmailDisclosure.onClose}
                    emailHook={emailHook}
                    clientData={clientData}
                  />
                </CustomModal>
              </div>
            </VStack>
          </Card.Body>
        </Card.Root>
      </Box>
    </>
  );
};

export default ProfileContent;
