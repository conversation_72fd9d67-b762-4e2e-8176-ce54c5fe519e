import { toaster } from '@/components/ui/toaster';
import { tableNames } from '@/constants/table_names';
import { ToastMessages } from '@/constants/toast-messages';
import supabase from '@/lib/supabase/client';
import { useDisclosure } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import {
  useAddClientActivityMutation,
  useAddNewEmailMutation,
  useUpdatePrimaryEmailFalseMutation,
  useUpdatePrimaryEmailTrueMutation,
} from './query';

interface ClientData {
  id: number;
  email: string;
  organization_id: number;
  client_id: number;
  client: {
    id: number;
    first_name: string;
    last_name: string;
    phone?: string;
  };
  client_emails?: {
    id: number;
    email: string;
    is_primary_email: boolean;
  }[];
}

export const useEmailHook = ({
  clientData,
  refetch,
}: {
  clientData: ClientData | null;
  refetch: any;
}) => {
  const [emailToEdit, setEmailToEdit] = useState<null | any>(null);
  const [hasMultiplePrimaryEmail, setHasMultiplePrimaryEmail] = useState(false);
  const [lookupEmail, setLookupEmail] = useState('');
  const [lookupEmailLoading, setLookupEmailLoading] = useState(false);

  const { mutateAsync: updatePrimaryEmailFalse } =
    useUpdatePrimaryEmailFalseMutation();
  const { mutateAsync: updatePrimaryEmailTrue } =
    useUpdatePrimaryEmailTrueMutation();
  const { mutateAsync: addNewEmailFunc } = useAddNewEmailMutation();
  useUpdatePrimaryEmailTrueMutation();
  const { mutateAsync: createClientActivity } = useAddClientActivityMutation();

  const addEmailDisclosure = useDisclosure();
  const editEmailDisclosure = useDisclosure();

  const [setPrimaryLoading, setSetPrimaryLoading] = useState({
    email: '',
    loading: false,
  });

  const setPrimaryEmail = async (clientId: number, email: string) => {
    try {
      setSetPrimaryLoading({ email, loading: true });
      const response = await fetch(
        `/api/public/client-emails/get-primary/${clientId}`,
        {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
        }
      );

      let isPrimaryEmail = null;
      if (response.ok) {
        isPrimaryEmail = await response.json();
      }

      if (isPrimaryEmail && isPrimaryEmail.id && response.ok) {
        const updateData = {
          id: isPrimaryEmail.id,
          is_primary_email: false,
        };

        await updatePrimaryEmailFalse(updateData);
      }

      const updateTrueData = {
        clientId: clientId,
        email: email,
        is_primary_email: true,
      };

      console.log('email', email);

      const res = await updatePrimaryEmailTrue(updateTrueData);

      if (!res) {
        return;
      }

      // Refetch data after updating
      if (res) {
        const activityInsertData = {
          client_id: clientId,
          activity_type: 'profile_updated',
          activity_date: new Date().toISOString(),

          details: {
            email: email,
            type: 'update_email',
          },
          organization_id: clientData?.organization_id,
        };

        await createClientActivity(activityInsertData);
        await refetch(); // Assuming `refetch` is defined in your component
      }

      toaster.create({
        description: ToastMessages.operationSuccess,
        type: 'success',
      });
    } catch (error) {
      toaster.create({ description: 'Something went wrong', type: 'error' });
    } finally {
      setSetPrimaryLoading({ email, loading: false });
    }
  };

  const removeAsPrimary = async (contactId: number, email: string) => {
    await supabase
      .from(tableNames.client_emails)
      .update({ is_primary_email: false })
      .eq('email', email)
      .eq('client_id', contactId);

    refetch();
  };

  // Email validation function
  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Check if email already exists for this client
  const checkEmailExists = async (email: string) => {
    const response = await fetch(
      `/api/public/client-emails/${email}?organization_id=${clientData?.organization_id}`,
      {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      }
    );
    // const { data } = await supabase
    //   .from(tableNames.client_emails)
    //   .select('id')
    //   .eq('email', email)
    //   .eq('client_id', clientId)
    //   .limit(1);

    const res = await response.json();
    // console.log('res', res);

    return res;
  };

  // Add new email function
  const addNewEmail = async (clientId: number): Promise<void> => {
    try {
      setLookupEmailLoading(true);
      const email = lookupEmail.trim();

      // Step 1: Validate email format
      if (!isValidEmail(email)) {
        toaster.create({
          description: 'Please enter a valid email address',
          type: 'error',
        });
        return;
      }

      // Step 2: Check if email already exists for this client
      const emailExists = await checkEmailExists(email);

      // console.log('emailExists', emailExists);

      if (emailExists && emailExists?.length > 0) {
        toaster.create({
          description: 'This email already exists.',
          type: 'error',
        });
        return;
      }

      // Step 3: Add the new email to client_emails table

      const newEmailInsert = {
        client_id: clientId,
        email: email,
        is_primary_email: false,
        organization_id: clientData?.organization_id,
      };

      // console.log('newEmailInsert', newEmailInsert);

      await addNewEmailFunc(newEmailInsert);

      // add activity

      const activityInsertData = {
        client_id: clientId,
        activity_type: 'profile_updated',
        activity_date: new Date().toISOString(),

        details: {
          email: email,
          type: 'new_email',
        },
        organization_id: clientData?.organization_id,
      };

      await createClientActivity(activityInsertData);

      // const { error } = await supabase.from(tableNames.client_emails).insert({
      //   client_id: clientId,
      //   email: email,
      //   is_primary_email: false, // New emails are not primary by default
      // });

      // Success - clear form and close modal
      setLookupEmail('');
      toaster.create({
        description: 'Email added successfully',
        type: 'success',
      });
      await refetch();
      addEmailDisclosure.onClose();
    } catch (error) {
      console.error('Error adding email:', error);
      toaster.create({
        description: 'Failed to add email. Please try again.',
        type: 'error',
      });
    } finally {
      setLookupEmailLoading(false);
    }
  };

  const onInputChange = (e: any) => {
    // console.log('e.target.value', e.target.value);
    setLookupEmail(e.target.value);
  };

  useEffect(() => {
    if (!clientData?.client_emails?.length) return;
    const numberOfMultiplePrimaryEmail = clientData?.client_emails?.filter(
      (item: any) => item.is_primary_email === true
    )?.length;
    setHasMultiplePrimaryEmail(numberOfMultiplePrimaryEmail > 1 ? true : false);
  }, [clientData]);

  // console.log('clientData in hook', clientData);

  return {
    setEmailToEdit,
    emailToEdit,
    setPrimaryEmail,
    removeAsPrimary,
    hasMultiplePrimaryEmail,
    setHasMultiplePrimaryEmail,
    addEmailDisclosure,
    editEmailDisclosure,
    setPrimaryLoading,
    // Add email related states and functions
    lookupEmail,
    lookupEmailLoading,
    onInputChange,
    addNewEmail,
  };
};
