'use client';

import { formatMoney } from '@/components/elements/format-money/FormatMoney';
import PageLoader from '@/components/elements/loader/PageLoader';
import { PDFGenerator } from '@/components/elements/pdf/PDF-Generator';
import { Box, Text } from '@chakra-ui/react';
import { PDFViewer } from '@react-pdf/renderer';
import moment from 'moment';
import { useRouter } from 'next/navigation';
import { useMemo } from 'react';
import { FiArrowLeft } from 'react-icons/fi';
import {
  useGetClientInvoiceByIdCM,
  useGetClientOrgByIdQueryCM,
} from '../../[organization_name]/me/profile/query';
interface ViewInvoiceProps {
  id: string | number;
}

interface Transaction {
  amount: string | number;
}

interface InvoiceData {
  client?: {
    display_name?: string;
    client_emails?: Array<{ email: string }>;
  };
  name?: string;
  email?: string;
  invoice_number?: string;
  transactions?: Transaction[];
  invoice_date?: string;
  due_date?: string;
  total_price?: string | number;
  organization_id: number;
  currency_code?: string;
  product?: string;
  referral?: string;
  qty?: string | number;
  memo?: string;
}

const ViewInvoice = ({ id }: ViewInvoiceProps) => {
  const { data: InvoiceData, isFetching: InvoiceDataLoading } =
    useGetClientInvoiceByIdCM(id, {
      enabled: !!id,
    });

  console.log('InvoiceData----4', InvoiceData);

  const { data: OrgData, isFetching: OrgDataLoading } =
    useGetClientOrgByIdQueryCM(InvoiceData?.data.organization_id, {
      enabled: !!InvoiceData?.data?.organization_id,
    });

  // console.log('OrgData', OrgData);

  const router = useRouter();

  // Memoize the processed invoice data to avoid recalculation on every render
  const processedInvoiceData = useMemo(() => {
    if (InvoiceDataLoading || !InvoiceData?.data) {
      return null;
    }

    const invoiceData: InvoiceData = InvoiceData?.data;

    // Calculate total paid amount
    const totalPaid =
      invoiceData.transactions?.reduce(
        (sum: number, transaction: Transaction) =>
          sum + Number(transaction?.amount || 0),
        0
      ) || 0;

    // Calculate amount due
    const totalPrice = Number(invoiceData.total_price || 0);
    const amountDue = totalPrice - totalPaid;

    // Format amount due
    const resolvedAmountDue = formatMoney(amountDue, {
      currencyCode: invoiceData.currency_code,
    });

    // Format dates once
    const formatDate = (dateString?: string, fallback?: string) => {
      const date = dateString || fallback;
      return date ? moment(date.split('T')[0]).format('MMMM D, YYYY') : '';
    };

    return {
      invoiceData,
      totalPaid,
      amountDue,
      resolvedAmountDue,
      formattedInvoiceDate: formatDate(invoiceData.invoice_date),
      formattedDueDate: formatDate(
        invoiceData.due_date,
        invoiceData.invoice_date
      ),
    };
  }, [InvoiceData, InvoiceDataLoading]);

  // Early return for loading state
  if (InvoiceDataLoading || !processedInvoiceData || OrgDataLoading) {
    return <PageLoader />;
  }

  const {
    invoiceData,
    amountDue,
    resolvedAmountDue,
    formattedInvoiceDate,
    formattedDueDate,
  } = processedInvoiceData;

  // Extract display values
  const clientName = invoiceData.client?.display_name || invoiceData.name || '';
  const clientEmail =
    invoiceData.client?.client_emails?.[0]?.email || invoiceData.email || '';

  return (
    <Box h="100vh" width={{ md: '90%', lg: '70%' }} mx={'auto'}>
      <Box
        display={'flex'}
        gap={'1rem'}
        cursor={'pointer'}
        onClick={() => router.back()}
        alignItems={'center'}
        m={3}
      >
        <FiArrowLeft />
        <Text fontWeight={'bold'}>Go Back</Text>
      </Box>

      {invoiceData?.organization_id === 1 ? (
        <PDFViewer
          style={{
            height: '100%',
            width: '100%',
            border: 'none',
          }}
          showToolbar
        >
          <PDFGenerator
            isPublic={true}
            publicOrgData={OrgData}
            name={clientName}
            email={clientEmail}
            receiptNumber={invoiceData.invoice_number || ''}
            transactions={invoiceData.transactions || []}
            date={formattedInvoiceDate}
            dueDate={formattedDueDate}
            amountDue={amountDue}
            resolvedAmountDue={resolvedAmountDue}
            activity={invoiceData.product || ''}
            invoice={invoiceData}
            referral={invoiceData.referral || ''}
            quantity={Number(invoiceData.qty || 0)}
            rate={Number(invoiceData.total_price || 0)}
            balance={0}
            memo={invoiceData.memo || ''}
          />
        </PDFViewer>
      ) : (
        <PDFViewer
          style={{
            height: '100%',
            width: '100%',
            border: 'none',
          }}
          showToolbar
        >
          <PDFGenerator
            isPublic={true}
            publicOrgData={OrgData}
            name={clientName}
            email={clientEmail}
            receiptNumber={String(invoiceData?.invoice_number)}
            transactions={invoiceData?.transactions ?? []}
            date={String(
              moment(invoiceData?.invoice_date?.split('T')[0]).format(
                'MMMM D, YYYY'
              )
            )}
            dueDate={String(
              moment(
                invoiceData?.due_date?.split('T')[0] ||
                  invoiceData?.invoice_date?.split('T')[0]
              ).format('MMMM D, YYYY')
            )}
            resolvedAmountDue={resolvedAmountDue}
            amountDue={amountDue}
            activity={String(invoiceData?.product || '')}
            invoice={invoiceData}
            referral={invoiceData?.referral ?? ''}
            quantity={Number(invoiceData?.qty || 0)}
            rate={Number(invoiceData?.total_price || 0)}
            balance={0}
            memo={String(invoiceData?.memo || '')}
          />
        </PDFViewer>
      )}
    </Box>
  );
};

export default ViewInvoice;
