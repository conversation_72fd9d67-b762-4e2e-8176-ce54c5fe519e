import { env } from '@/constants/env';
import { tableNames } from '@/constants/table_names';
import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export const dynamic = 'force-dynamic';
export const revalidate = 0;

export async function GET(
  request: Request,
  { params }: { params: { clientId: string } }
) {
  const { clientId } = params;

  console.log('clientId', clientId);

  // Get the specific email record first
  const { data, error } = await supabaseAdmin
    .from(tableNames.client_emails)
    .select('*')
    .eq('client_id', clientId)
    .eq('is_primary_email', true)
    .single();

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  if (!data) {
    return NextResponse.json(null);
  }

  return NextResponse.json(data);
}
