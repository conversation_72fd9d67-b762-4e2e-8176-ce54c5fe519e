export const dynamic = 'force-dynamic';
import { env } from '@/constants/env';
import { tableNames } from '@/constants/table_names';
import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const {
      firstName,
      lastName,
      phone,
      middleName,
      displayName,
      address,
      dob,
      postalCode,
      country,
      state,
      city,
      clientId,
    } = body;

    //Update the clients table
    const { error: clientError } = await supabaseAdmin
      .from(tableNames.clients)
      .update({
        first_name: firstName?.trim() || '',
        last_name: lastName?.trim() || '',
        middle_name: middleName?.trim() || '',
        display_name: displayName,
        address: address,
        province: '',
        dob: dob,
        postal_code: postalCode,
        country: country,
        state: state,
        city: city,
        phone: phone,
      })
      .eq('id', Number(clientId));

    if (clientError) {
      console.error('Client update error:', clientError);
      return NextResponse.json({ error: clientError.message }, { status: 500 });
    }

    return NextResponse.json({
      message: 'Profile updated successfully',
    });
  } catch (error) {
    console.error('PUT request error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
