export const dynamic = 'force-dynamic';
import { env } from '@/constants/env';
import { tableNames } from '@/constants/table_names';
import { getNumberParam } from '@/utils/format-object';
import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(
  request: Request,
  { params }: { params: { id: string; org_id: string } }
) {
  const { id, org_id } = params;

  const url = new URL(request.url);

  // Pagination params with validation
  const currentPage = getNumberParam(url.searchParams, 'page_number', 1);
  const itemsPerPage = getNumberParam(url.searchParams, 'items_per_page', 50);

  // Validate pagination bounds
  if (currentPage < 1) {
    return NextResponse.json(
      { error: 'page_number must be greater than 0' },
      { status: 400 }
    );
  }

  if (itemsPerPage < 1 || itemsPerPage > 100) {
    return NextResponse.json(
      { error: 'items_per_page must be between 1 and 100' },
      { status: 400 }
    );
  }

  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage - 1;
  const { data, error } = await supabaseAdmin
    .from(tableNames.bookings)
    .select(`*`)
    .eq('client_id', Number(id))
    .eq('organization_id', Number(org_id))
    .range(startIndex, endIndex)
    .order('created_at', { ascending: false });

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  return NextResponse.json(data);
}
