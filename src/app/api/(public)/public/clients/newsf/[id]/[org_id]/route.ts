export const dynamic = 'force-dynamic';
import { env } from '@/constants/env';
import { tableNames } from '@/constants/table_names';
import { getNumberParam } from '@/utils/format-object';
import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

const resolveTotalPrice = (invoice: any, subtotal: number) => {
  if (invoice?.total_price) {
    return Number(invoice.total_price);
  }

  return subtotal;
};

export async function GET(
  request: Request,
  { params }: { params: { id: string; org_id: string } }
) {
  const { id, org_id } = params;
  const url = new URL(request.url);

  // Pagination params with validation
  const currentPage = getNumberParam(url.searchParams, 'page_number', 1);
  const itemsPerPage = getNumberParam(url.searchParams, 'items_per_page', 50);

  // Validate pagination bounds
  if (currentPage < 1) {
    return NextResponse.json(
      { error: 'page_number must be greater than 0' },
      { status: 400 }
    );
  }

  if (itemsPerPage < 1 || itemsPerPage > 100) {
    return NextResponse.json(
      { error: 'items_per_page must be between 1 and 100' },
      { status: 400 }
    );
  }

  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage - 1;

  const {
    data: invoices,
    error,
    count,
  } = await supabaseAdmin
    .from(tableNames.invoices)
    .select(
      `
       *,
    client:clients (*, client_emails (*)),
    transactions!transactions_invoice_id_fkey (*),
    invoice_items (
      *,
      package_offering (*, package_items (services (*))),
      services (*),
      taxes (*)
    )
        `,
      {
        count: 'exact',
      }
    )
    .eq('client_id', Number(id))
    .eq('organization_id', Number(org_id))
    .order('created_dt', { ascending: false })
    .range(startIndex, endIndex);

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  // Handle empty results
  if (!invoices) {
    return NextResponse.json({
      data: [],
      pagination: {
        page_number: currentPage,
        total_count: 0,
        items_per_page: itemsPerPage,
      },
    });
  }
  const updatedInvoices = invoices.map((invoice: any) => {
    let amountDue = 0;

    // Calculate discount amount if discount exists
    // let discountAmount = 0;
    const subtotal = invoice?.invoice_items?.reduce(
      (sum: number, item: any) => {
        const newPrice = Number(item?.quantity || 0) * Number(item?.price || 0);
        return sum + newPrice;
      },
      0
    );
    // if (invoice.discount && typeof invoice.discount === 'object') {
    //   if (invoice.discount.value > 0) {
    //     if (invoice.discount.type === 'percentage') {
    //       discountAmount = (subtotal * invoice.discount.value) / 100;
    //     } else {
    //       discountAmount = invoice.discount.value;
    //     }
    //   }
    // } else if (typeof invoice.discount === 'number' && invoice.discount > 0) {
    //   discountAmount = invoice.discount;
    // }

    if (invoice.transactions && invoice.transactions.length > 0) {
      const totalPaid = invoice.transactions.reduce(
        (sum: number, transaction: any) =>
          sum + Number(transaction.amount || 0),
        0
      );
      const totalPriceWithDiscount = Number(invoice.total_price);
      amountDue = totalPriceWithDiscount - totalPaid;
    } else {
      amountDue = Number(invoice.total_price);
    }

    // Calculate total_duration_minutes for this invoice
    let totalDuration = 0;
    for (const item of invoice.invoice_items || []) {
      if (item.services && typeof item.services.duration_minutes === 'number') {
        totalDuration += item.services.duration_minutes;
      } else if (
        item.package_offering &&
        Array.isArray(item.package_offering.package_items)
      ) {
        for (const pkgItem of item.package_offering.package_items) {
          if (
            pkgItem.services &&
            typeof pkgItem.services.duration_minutes === 'number'
          ) {
            totalDuration += pkgItem.services.duration_minutes;
          }
        }
      }
    }

    const resolvedTotalPrice = resolveTotalPrice(invoice, subtotal);

    return {
      ...invoice,
      amount_due: amountDue,
      total_duration_minutes: totalDuration, // <-- Add this property
      total_price: resolvedTotalPrice,
    };
  });

  return NextResponse.json({
    data: updatedInvoices,
    pagination: {
      page_number: currentPage,
      total_count: count || 0,
      items_per_page: itemsPerPage,
    },
  });
}
