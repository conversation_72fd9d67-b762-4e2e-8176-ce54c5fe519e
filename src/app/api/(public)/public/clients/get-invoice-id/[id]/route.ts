export const dynamic = 'force-dynamic';
import { env } from '@/constants/env';
import { tableNames } from '@/constants/table_names';
import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(
  request: Request,
  { params }: { params: { id: any } }
) {
  try {
    const { id } = params;
    if (!id) {
      return NextResponse.json(
        { error: 'Invoice ID is required' },
        { status: 400 }
      );
    }

    // 1. Fetch the invoice and related data (excluding taxes)
    const { data: invoice, error: queryError } = await supabaseAdmin
      .from(tableNames.invoices)
      .select(
        `
         *,
         client:clients (
           id,
           display_name,
           phone,
           country,
           state,
           city,
           client_emails (*)
         ),
         transactions!transactions_invoice_id_fkey(*),
         invoice_items (
           *,
           package_offering (
             *
           ),
           services (
             *
           )
         ),
         services_purchases!services_purchases_invoice_id_fkey(*,service:services!service_id(*), invoice_items(*))
         `
      )
      .eq('id', Number(id))
      .single();

    if (queryError) {
      console.error('Supabase query error:', queryError);
      return NextResponse.json(
        {
          error: 'Failed to fetch invoice',
          code: queryError.code,
        },
        { status: 500 }
      );
    }

    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    // 2. Collect all tax_ids across all invoice_items
    const allTaxIds = Array.from(
      new Set(
        invoice.invoice_items
          .flatMap((item: any) => item.tax_ids || [])
          .filter(Boolean)
      )
    );

    // 3. Fetch taxes separately
    const { data: taxes, error: taxError } = await supabaseAdmin
      .from('taxes')
      .select('*')
      .in('id', allTaxIds);

    if (taxError) {
      console.error('Failed to fetch taxes:', taxError);
      return NextResponse.json(
        { error: 'Failed to fetch taxes', code: taxError.code },
        { status: 500 }
      );
    }

    // 4. Map tax_ids to full tax objects and attach to each invoice_item
    const taxMap = new Map<number, any>();
    for (const tax of taxes || []) {
      taxMap.set(tax.id, tax);
    }

    invoice.invoice_items = invoice.invoice_items.map((item: any) => ({
      ...item,
      taxes: (item.tax_ids || [])
        .map((taxId: number) => taxMap.get(taxId))
        .filter(Boolean),
    }));

    return NextResponse.json({ data: invoice });
  } catch (error: any) {
    console.error('Unexpected error in GET /api/invoices/[id]:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        ...(process.env.NODE_ENV === 'development' && {
          details: error.message,
        }),
      },
      { status: 500 }
    );
  }
}
