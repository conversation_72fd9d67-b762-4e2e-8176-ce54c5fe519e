import { createExternalApiLogs } from '@/app/service/api-logs';
import { env } from '@/constants/env';
import { tableNames } from '@/constants/table_names';
import { createClient } from '@supabase/supabase-js';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

const supabase = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);
const WEBHOOK_API_KEY = env.WEBHOOK_API_KEY;
export async function POST(req: NextRequest) {
  // Verify API key
  const apiKey = req.headers.get('x-api-key');
  if (!apiKey || apiKey !== WEBHOOK_API_KEY) {
    return NextResponse.json(
      { message: 'Invalid or missing API key' },
      { status: 401 }
    );
  }

  const body = await req.json();

  try {
    const { email, client_name, phone } = body;
    await createExternalApiLogs(
      {
        source: 'general',
        domain: 'general',
        raw_data: body,
      },
      supabase
    );

    // Search for existing client using email
    const { data: ClientEmail, error: ClientEmailError } = await supabase
      .from(tableNames.client_emails)
      .select(`email, clients(*)`)
      .eq('email', email)
      .maybeSingle();
    if (ClientEmailError) throw ClientEmailError;

    if (ClientEmail) {
      //Client Exist
      const clientDetails = ClientEmail.clients?.[0];
      // Update client null details
      if (!clientDetails.display_name || !clientDetails?.phone) {
        await supabase
          .from(tableNames.clients)
          .update({
            ...(!clientDetails?.phone && {
              phone: phone,
            }),
            ...(!clientDetails?.display_name && {
              display_name: client_name,
            }),
          })
          .eq('id', clientDetails.id);
      }
    } else {
      //Client does not exist
      const payload = {
        phone,
        display_name: client_name,
        first_name: client_name?.split(' ')?.[0],
        last_name: client_name?.split(' ')?.[1],
        organization_id: body?.organization_id || 1,
      };

      // Create client
      const { data: createdClient } = await supabase
        .from(tableNames.clients)
        .insert(payload)
        .select('*')
        .maybeSingle();

      // Create client emails
      const { error: clientEmailError } = await supabase
        .from(tableNames.client_emails)
        .insert({
          client_id: createdClient.id,
          email: email?.toLowerCase(),
          is_primary_email: true,
          organization_id: body?.organization_id || 1,
        });
      if (clientEmailError) throw clientEmailError;

      //Create Client Activities

      const activitiesPayload = {
        client_id: createdClient?.id,
        activity_type: 'client_created',
        details: {
          created_by: 'general',
          ...body,
        },
        organization_id: body?.organization_id || 1,
        activity_date: new Date().toISOString(),
      };
      const { error: activityUploadError } = await supabase
        .from(tableNames.client_activities)
        .insert(activitiesPayload);
      if (activityUploadError) throw activityUploadError;
    }

    return NextResponse.json({ message: 'Calendly updated successfully' });
  } catch (error: any) {
    console.log('Error is ', error);
    return NextResponse.json({ message: error.message });
  }
}
