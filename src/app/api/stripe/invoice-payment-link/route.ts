// app/api/generate-payment-link/route.ts
// API route to generate payment link (called from authenticated user interface)

import { tableNames } from '@/constants/table_names';
import { stripe } from '@/lib/stripe';
import { createSupabaseServer } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const { invoiceId, user_id, organization_name } = await req.json();

    const supabase = createSupabaseServer();

    // Get current user (pseudo-code, replace with your auth)

    // Insert into payment_links
    const { data: paymentLinkExist } = await supabase
      .from(tableNames.payment_links)
      .select('*')
      .eq('invoice_id', invoiceId)
      .single();
    if (paymentLinkExist && paymentLinkExist.status === 'pending') {
      const link = `${process.env.NEXT_PUBLIC_SITE_URL}/resolve/invoice/${paymentLinkExist.id}${organization_name ? `?org_slug=${organization_name}` : ''}`;
      return NextResponse.json({ link });
    }
    if (paymentLinkExist && paymentLinkExist.status === 'paid') {
      return NextResponse.json(
        { error: 'Intent already paid' },
        { status: 500 }
      );
    }
    //fetch user
    const { data: user } = await supabase
      .from(tableNames.users)
      .select('*')
      .eq('id', Number(user_id))
      .single();
    const { data: organization } = await supabase
      .from(tableNames.organizations)
      .select('*')
      .eq('id', Number(user?.organization_id))

      .single();

    // console.log('user is ', user);
    // console.log('organization is ', organization);

    if (!user || !organization) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 404 });
    }
    if (!organization.stripe_user_id) {
      return NextResponse.json(
        { error: 'Please connect stripe' },
        { status: 404 }
      );
    }

    // Fetch invoice and check ownership
    const { data: invoice, error: invoiceError } = await supabase
      .from(tableNames.invoices)
      .select('*')
      .eq('id', invoiceId)
      .maybeSingle();

    if (invoiceError || !invoice) {
      return NextResponse.json(
        { error: 'Invoice not found or not owned by user' },
        { status: 404 }
      );
    }

    const paymentIntent = await stripe.paymentIntents.create(
      {
        amount: invoice.total_price * 100,
        currency: invoice.currency_code || 'usd', // Adjust currency
        on_behalf_of: organization.stripe_user_id,
        payment_method_types: ['card'],
        transfer_data: {
          destination: organization.stripe_user_id,
        },

        metadata: {
          invoice_id: invoiceId,
          client_id: invoice?.client_id,
          user_id,
          organization_id: user?.organization_id,
          organization_name,
        },
        application_fee_amount: 0,
      }
      // { stripeAccount: organization.stripe_user_id }
    );

    // Insert into payment_links
    const { data: paymentLink, error: linkError } = await supabase
      .from(tableNames.payment_links)
      .insert({
        invoice_id: invoiceId,
        payment_intent_id: paymentIntent.id,
        user_id,
        stripe_user_id: organization.stripe_user_id,
      })
      .select()
      .single();

    if (linkError) {
      return NextResponse.json(
        { error: 'Error creating payment link' },
        { status: 500 }
      );
    }

    const link = `${process.env.NEXT_PUBLIC_SITE_URL}/resolve/invoice/${paymentLink.id}${organization_name ? `?org_slug=${organization_name}` : ''}`;
    return NextResponse.json({ link });
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
