import { OAuth2Client } from 'google-auth-library';
import { fetchAndInsertEmails } from '../gmail/utils';
import { tableNames } from '@/constants/table_names';

const clientId = process.env.GMAIL_CLIENT_ID as string;
const clientSecret = process.env.GMAIL_SECRET_KEY as string;
const redirectUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`;

function decodeBody(body: any) {
  if (!body) return '';
  let content = body.data;
  if (body.encoding === 'base64url') {
    content = content.replace(/-/g, '+').replace(/_/g, '/');
  }

  return Buffer.from(content, 'base64').toString();
}

export async function fetchMessagesByOptimizedQuery(
  gmail: any,
  maxResults: number,
  query: string
) {
  const now = Math.floor(Date.now() / 1000);
  const twoYearsAgo = now - 2 * 365 * 24 * 60 * 60;

  try {
    const res = await gmail.users.messages.list({
      userId: 'me',
      maxResults,
      q: `(to:"${query}" OR from:"${query}") after:${twoYearsAgo} before:${now} (in:inbox OR in:sent OR in:draft)`,
      fields: 'messages(id,threadId,labelIds)',
    });

    return res.data.messages || [];
  } catch (error) {
    console.warn('Failed to fetch messages:', error);
    return [];
  }
}

export async function optimizedBackgroundSync(
  supabase: any,
  gmail: any,
  messageIds: string[],
  query: string,
  tokens: any
) {
  try {
    console.log(
      'Optimized background sync started for',
      messageIds.length,
      'emails'
    );

    const emailDetailsList = await batchFetchEmailDetails(gmail, messageIds);

    const { combinedEmails, clientDetails } = await processEmailsOptimized(
      gmail,
      emailDetailsList,
      query,
      supabase
    );

    await fetchAndInsertEmails(supabase, clientDetails, tokens, combinedEmails);

    console.log('Optimized background sync completed successfully');
  } catch (error) {
    console.error('Optimized background sync failed:', error);
  }
}

async function batchFetchEmailDetails(gmail: any, messageIds: string[]) {
  const BATCH_SIZE = 50;
  const allEmailDetails = [];

  for (let i = 0; i < messageIds.length; i += BATCH_SIZE) {
    const batch = messageIds.slice(i, i + BATCH_SIZE);

    const batchResults = await Promise.all(
      batch.map(async (messageId) => {
        try {
          return await fetchEmailDetailsOptimized(gmail, messageId);
        } catch (error) {
          console.warn(`Failed to fetch email ${messageId}:`, error);
          return null;
        }
      })
    );

    allEmailDetails.push(...batchResults.filter(Boolean));

    if (i + BATCH_SIZE < messageIds.length) {
      await new Promise((resolve) => setTimeout(resolve, 100));
    }
  }

  return allEmailDetails;
}

async function fetchEmailDetailsOptimized(gmail: any, messageId: string) {
  const email = await gmail.users.messages.get({
    userId: 'me',
    id: messageId,
    format: 'full',
    fields:
      'id,threadId,labelIds,snippet,payload(headers,body,mimeType,parts(mimeType,body))',
  });

  const headers = email.data.payload?.headers || [];
  const subject = headers.find((h: any) => h.name === 'Subject')?.value || '';
  const from = headers.find((h: any) => h.name === 'From')?.value || '';
  const to = headers.find((h: any) => h.name === 'To')?.value || '';
  const date = headers.find((h: any) => h.name === 'Date')?.value || '';
  const cc = headers.find((h: any) => h.name === 'Cc')?.value || '';
  const bcc = headers.find((h: any) => h.name === 'Bcc')?.value || '';

  const { html, attachments } = parseEmailParts(
    email.data.payload?.parts || [
      {
        mimeType: email.data.payload?.mimeType,
        body: email.data.payload?.body,
      },
    ]
  );

  const attachmentMetadata = attachments.map((attachment) => ({
    ...attachment,
    data: null,
    downloadOnDemand: true,
  }));

  return {
    id: messageId,
    threadId: email.data.threadId,
    labelIds: email.data.labelIds,
    snippet: email.data.snippet,
    subject,
    from,
    to,
    date,
    html,
    attachments: attachmentMetadata,
    cc,
    bcc,
  };
}

async function fetchThreadData(gmail: any, threadId: string) {
  const thread = await gmail.users.threads.get({
    userId: 'me',
    id: threadId,
    format: 'full',
    fields:
      'messages(id,threadId,labelIds,snippet,payload(headers,body,mimeType,parts(mimeType,body)),internalDate)',
  });

  const threadMessages =
    thread.data.messages?.map((message: any) => {
      const headers = message.payload?.headers || [];
      const subject =
        headers.find((h: any) => h.name === 'Subject')?.value || '';
      const from = headers.find((h: any) => h.name === 'From')?.value || '';
      const to = headers.find((h: any) => h.name === 'To')?.value || '';
      const date = headers.find((h: any) => h.name === 'Date')?.value || '';
      const cc = headers.find((h: any) => h.name === 'Cc')?.value || '';
      const bcc = headers.find((h: any) => h.name === 'Bcc')?.value || '';

      const { html, attachments } = parseEmailParts(
        message.payload?.parts || [
          { mimeType: message.payload?.mimeType, body: message.payload?.body },
        ]
      );

      const attachmentMetadata = attachments.map((a) => ({
        ...a,
        data: null,
        downloadOnDemand: true,
      }));

      return {
        id: message.id,
        threadId: message.threadId,
        labelIds: message.labelIds,
        snippet: message.snippet,
        subject,
        from,
        to,
        date,
        html,
        attachments: attachmentMetadata,
        cc,
        bcc,
      };
    }) || [];

  return threadMessages;
}

async function processEmailsOptimized(
  gmail: any,
  emailDetailsList: any[],
  query: string,
  supabase: any
) {
  const threadMap = new Map<string, any>();
  const standaloneEmails: any[] = [];

  const threadFetchPromises: Promise<void>[] = [];

  for (const emailDetails of emailDetailsList) {
    const { threadId, id } = emailDetails;

    if (threadId) {
      if (!threadMap.has(threadId)) {
        // Deduplicate thread fetch calls
        threadFetchPromises.push(
          (async () => {
            const threadData = await fetchThreadData(gmail, threadId);
            const filteredMessages = threadData.filter(
              (msg: any) => msg.id !== id
            );

            threadMap.set(threadId, {
              ...emailDetails,
              threadMessages: filteredMessages,
            });
          })()
        );
      }
    } else {
      standaloneEmails.push(emailDetails);
    }
  }

  // Wait for all unique thread fetches
  await Promise.all(threadFetchPromises);

  const combinedEmails = [
    ...standaloneEmails,
    ...Array.from(threadMap.values()),
  ];

  const clientDetails = await fetchClientDetails(supabase, query);

  return { combinedEmails, clientDetails };
}

async function fetchClientDetails(supabase: any, query: string) {
  const { data: clientDetails } = await supabase
    .from(tableNames.client_emails)
    .select('client_id, organization_id')
    .eq('email', query)
    .single();

  return clientDetails;
}

function parseEmailParts(parts: any[]): { html: string; attachments: any[] } {
  let html = '';
  const attachments: any[] = [];

  if (!parts) return { html, attachments };

  for (const part of parts) {
    if (part.mimeType === 'text/html') {
      html = decodeBody(part.body);
    } else if (part.mimeType === 'multipart/alternative' && part.parts) {
      const parsed = parseEmailParts(part.parts);
      html = parsed.html;
    } else if (part.body?.attachmentId) {
      attachments.push({
        id: part.body.attachmentId,
        mimeType: part.mimeType,
        filename: part.filename,
        size: part.body.size,
      });
    }
  }

  return { html, attachments };
}

export async function validateAndRefreshTokens(tokens: any) {
  try {
    const controller = new AbortController();
    setTimeout(() => controller.abort(), 3000);

    const isAccessTokenValid = await fetch(
      'https://www.googleapis.com/oauth2/v1/tokeninfo',
      {
        method: 'GET',
        headers: { Authorization: `Bearer ${tokens.google_access_token}` },
        signal: controller.signal,
      }
    );

    if (!isAccessTokenValid.ok) {
      const oauth2Client = new OAuth2Client(
        clientId,
        clientSecret,
        redirectUrl
      );
      oauth2Client.setCredentials({
        refresh_token: tokens.google_refresh_token,
      });
      const { credentials } = await oauth2Client.refreshAccessToken();
      return {
        ...tokens,
        google_access_token: credentials.access_token,
      };
    }

    return tokens;
  } catch {
    const oauth2Client = new OAuth2Client(clientId, clientSecret, redirectUrl);
    oauth2Client.setCredentials({
      refresh_token: tokens.google_refresh_token,
    });
    const { credentials } = await oauth2Client.refreshAccessToken();
    return {
      ...tokens,
      google_access_token: credentials.access_token,
    };
  }
}
