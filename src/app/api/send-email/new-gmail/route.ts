import { getUserByEmail } from '@/app/service/user';
import { createSupabaseServer } from '@/lib/supabase/server';
import { OAuth2Client } from 'google-auth-library';
import { google } from 'googleapis';
import { NextRequest, NextResponse } from 'next/server';
import { fetchEmails } from '../gmail/utils';
import {
  fetchMessagesByOptimizedQuery,
  validateAndRefreshTokens,
} from './new-utils';

const clientId = process.env.GMAIL_CLIENT_ID as string;
const clientSecret = process.env.GMAIL_SECRET_KEY as string;
const redirectUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`;

export async function GET(req: NextRequest) {
  const startTime = Date.now();
  const { searchParams } = req.nextUrl;
  const maxResults = Number(searchParams.get('maxResults') || 10);
  const query = searchParams.get('query') || '';

  try {
    if (query.trim() === '') {
      return NextResponse.json(
        { message: 'No user email query provided' },
        { status: 400 }
      );
    }

    const supabase = createSupabaseServer();
    const { data: userRes } = await supabase.auth.getUser();
    const userEmail = userRes?.user?.email;

    if (!userEmail) {
      return NextResponse.json(
        { message: 'User not authenticated' },
        { status: 401 }
      );
    }

    const tokens = await getUserByEmail(userEmail);

    if (!tokens?.google_refresh_token) {
      return NextResponse.json(
        {
          message:
            'No tokens found, please go to the integration page and grant access',
        },
        { status: 401 }
      );
    }

    const validatedTokens = await validateAndRefreshTokens(tokens);

    const oauth2Client = new OAuth2Client(clientId, clientSecret, redirectUrl);
    oauth2Client.setCredentials({
      access_token: validatedTokens.google_access_token,
      refresh_token: validatedTokens.google_refresh_token,
    });

    const gmail = google.gmail({ version: 'v1', auth: oauth2Client });

    if (!query) {
      const emails = await fetchEmails(gmail, maxResults);
      return NextResponse.json({ messages: emails });
    }

    console.log(
      `[${Date.now() - startTime}ms] Starting optimized Gmail API call`
    );

    const messages = await fetchMessagesByOptimizedQuery(
      gmail,
      maxResults,
      query
    );

    console.log(`[${Date.now() - startTime}ms] Gmail API call completed`);

    const uniqueMessageIds = [
      ...new Set(messages.map((msg: any) => msg.id).filter(Boolean)),
    ];

    return NextResponse.json({
      data: {
        userEmail,
        uniqueMessageIds,
        query,
        tokens: validatedTokens,
      },
      syncStatus: 'processing',
      info: 'background sync in progress',
    });
  } catch (error: any) {
    console.error('Gmail API error:', error);
    return NextResponse.json(
      { message: 'Failed to read emails', error: error?.message || error },
      { status: 500 }
    );
  }
}
