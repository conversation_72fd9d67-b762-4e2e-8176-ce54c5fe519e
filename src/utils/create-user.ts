import { toaster } from '@/components/ui/toaster';
import { tableNames } from '@/constants/table_names';
import { insertRow, selectOneRow } from '@/lib/supabase/utils';
import { SupabaseClient } from '@supabase/supabase-js';

interface ClientEmailData {
  client_id: string | number;
  email: string;
  orgIdNum?: number | null;
}

interface CreateUserOptions {
  clientData: any;
  validate?: boolean;
  showToast?: boolean;
  supabaseClient?: SupabaseClient;
  clientExistsError?: string;
  createEmail?: boolean;
}

interface CheckEmailOptions {
  email: string;
  select?: string;
  showToast?: boolean;
  supabaseClient?: SupabaseClient;
  clientExistsError?: string;
}

export const createUser = async ({
  clientData,
  validate = true,
  showToast = true,
  supabaseClient,
  clientExistsError = 'Email already exists.',
  createEmail = true,
}: CreateUserOptions) => {
  const { email, organization_id, ...clientPayload } = clientData;

  if (!email && createEmail) throw new Error('Email is not provided!');

  let orgIdNum: number | null = null;
  if (organization_id !== undefined && organization_id !== null) {
    orgIdNum = Number(organization_id);
    if (isNaN(orgIdNum)) {
      throw new Error('Invalid organization_id: must be a number');
    }
  }

  if (validate) {
    const existing = await checkIfEmailExist({
      email,
      select: 'id, email',
      showToast,
      supabaseClient,
    });
    if (existing) throw new Error(clientExistsError);
  }

  const newClient = await insertRow<any>(
    tableNames.clients,
    {
      ...clientPayload,
      ...(orgIdNum !== null ? { organization_id: orgIdNum } : {}),
    },
    supabaseClient
  );

  if (createEmail && newClient.id) {
    console.log('proceed to create email', newClient.id);
    await createClientEmails(
      { client_id: newClient.id, email, orgIdNum: orgIdNum },
      supabaseClient
    );
  }

  return newClient;
};

export const checkIfEmailExist = async <T = any>({
  email,
  select = '*',
  showToast = true,
  supabaseClient,
  clientExistsError = 'Email already exists.',
}: CheckEmailOptions): Promise<T | null> => {
  const record = await selectOneRow<T>(
    tableNames.client_emails,
    [{ column: 'email', operator: 'eq', value: email.toLowerCase() }],
    select,
    supabaseClient
  );

  if (record && showToast) {
    toaster.create({
      description: clientExistsError,
      type: 'error',
    });
  }

  return record;
};

export const createClientEmails = async (
  emailData: ClientEmailData,
  supabaseClient?: SupabaseClient
) => {
  const { client_id, email, orgIdNum } = emailData;
  console.log('inside create email function', emailData);

  const record = await insertRow(
    tableNames.client_emails,
    {
      client_id,
      email,
      ...(orgIdNum !== null && orgIdNum !== undefined
        ? { organization_id: orgIdNum }
        : {}),
      is_primary_email: true,
    },
    supabaseClient
  );

  return record;
};
