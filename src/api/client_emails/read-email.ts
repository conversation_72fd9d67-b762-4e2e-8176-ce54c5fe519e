import { queryKey } from '@/constants/query-key';
import {
  queryClient,
  QueryConfigType,
  useMutation,
  useQuery,
} from '@/lib/react-query';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';

type IGetEmailsFilter = {
  maxResults?: string;
  query: string;
  page?: number;
  pageSize?: number;
};

// async function getEmails(filter: IGetEmailsFilter) {
//   const baseUrl = `/api/send-email/gmail`;
//   const apiUrl = buildUrlWithQueryParams(baseUrl, filter);
//   const response = await fetch(apiUrl, {
//     method: 'GET',
//   });
//   const data: any = await response.json();
//   return data;
// }
async function getEmails(filter: IGetEmailsFilter) {
  const baseUrl = `/api/send-email/new-gmail`;
  const apiUrl = buildUrlWithQueryParams(baseUrl, filter);

  try {
    const response = await fetch(apiUrl, {
      method: 'GET',
    });

    const data = await response.json();
    return data;
  } catch (error: any) {
    if (error.name === 'AbortError') {
      console.error('Request aborted by timeout');
    } else {
      console.error('Request failed:', error);
    }
    throw error;
  }
}

type QueryFnType = typeof getEmails;

export const useGetEmailsQuery = (
  filter: IGetEmailsFilter,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery({
    retry(failureCount: any, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.emails.readEmails, filter],
    queryFn: () => getEmails(filter),
    ...config,
  });
};

// export const useGetEmailsQuery = (
//   filter: IGetEmailsFilter,
//   config?: QueryConfigType<QueryFnType>
// ) => {
//   return useQuery({
//     retry(failureCount: any, error: any) {
//       // Only retry on network errors, not timeouts or server errors
//       if (
//         [401, 404, 408, 500, 504].includes(error.status) ||
//         error.message.includes('timeout')
//       ) {
//         return false;
//       }
//       return failureCount < 1;
//     },
//     staleTime: 2 * 60 * 1000, // 2 minutes
//     cacheTime: 5 * 60 * 1000, // 5 minutes
//     queryKey: [queryKey.emails.readEmails, filter],
//     queryFn: () => getEmails(filter),
//     ...config,
//   });
// };

export const useProcessEmailMutation = () => {
  return useMutation({
    mutationFn: async (data: any) => {
      const response = await fetch('/api/send-email/sync-emails', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`Failed to sync emails: ${response.statusText}`);
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [queryKey.emails.readDBEmails],
      });
    },
  });
};
