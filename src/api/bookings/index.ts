/* eslint-disable no-constant-condition */
import { tableNames } from '@/constants/table_names';
import supabase from '@/lib/supabase/client';

export const findAllBookingsByClientId = async (client_id: number) => {
  // Query the 'bookings' table for entries with the provided client_id
  const { data, error } = await supabase
    .from(tableNames.bookings)
    .select(`*`)
    .is('client_id', client_id);
  if (error) throw error;
  return data;
};

export const updateClientIdOnBookingsById = async (
  booking_id: number,
  client_id: number
) => {
  if (!booking_id || !client_id) throw new Error('Missing Info');

  // Update the 'client_id' for the booking with the provided 'booking_id'
  const { error } = await supabase
    .from(tableNames.bookings)
    .update({ client_id: client_id })
    .eq('id', booking_id);

  if (error) throw error;
};

export const findAllRawAppointmentsFromBookings = async () => {
  let allData: any = [];
  const fetchLimit = 1000; // Number of records to fetch per iteration
  let offset = 0;

  while (true) {
    const { data, error } = await supabase
      .from(tableNames.bookings)
      .select('*')
      .is('appointment', null) // Add this filter condition
      .range(offset, offset + fetchLimit - 1);

    if (error) throw error;

    if (data.length === 0) {
      break; // No more data, break out of the loop
    }

    allData = allData.concat(data);
    offset += fetchLimit;
  }

  return allData;
};

export const findAllRawBookingCreatedAtFromBookings = async () => {
  let allData: any = [];
  const fetchLimit = 1000; // Number of records to fetch per iteration
  let offset = 0;

  while (true) {
    const { data, error } = await supabase
      .from(tableNames.bookings)
      .select('*')
      .is('created_at', null) // Add this filter condition
      .range(offset, offset + fetchLimit - 1);

    if (error) throw error;

    if (data.length === 0) {
      break; // No more data, break out of the loop
    }

    allData = allData.concat(data);
    offset += fetchLimit;
  }

  return allData;
};
export const findAllBookings = async () => {
  let allData: any = [];
  const fetchLimit = 1000; // Number of records to fetch per iteration
  let offset = 0;

  while (true) {
    const { data, error } = await supabase
      .from(tableNames.bookings)
      .select('*')
      .range(offset, offset + fetchLimit - 1);

    if (error) throw error;

    if (data.length === 0) {
      break; // No more data, break out of the loop
    }

    allData = allData.concat(data);
    offset += fetchLimit;
  }

  return allData;
};

// ========================
export const findExistingClient = async (email: string) => {
  const { data, error } = await supabase
    .from(tableNames.client_emails)
    .select(`clients(*), email`)
    .eq('email', email);
  if (error) throw error;
  return data as any;
};

export const findExistingClientById = async (id: any) => {
  const { data, error } = await supabase

    .from(tableNames.clients)
    .select(`*`)
    .eq('id', id);
  if (error) throw error;

  return data as any;
};

export const createClient = async (clientData: any) => {
  const { data, error } = await supabase
    .from(tableNames.clients)
    .insert(clientData)
    .select();
  if (error) throw error;
  return data;
};

export const createClientEmails = async (
  clientId: number,
  email: string,
  organization_id: any
) => {
  const { data, error } = await supabase
    .from(tableNames.client_emails)
    .insert({
      client_id: clientId,
      email: email,
      organization_id: organization_id,
    })
    .select();
  if (error) throw error;
  return data;
};

export const checkDuplicateBooking = async (
  booking: any,
  organization_id?: string
) => {
  let query = supabase
    .from(tableNames.bookings)
    .select('*')
    .eq('email', booking.email)
    .eq('event', booking.event)
    .eq('assigned_to', booking.assigned_to)
    .eq('appointment', booking.appointment);

  // Add organization_id filter only if it's provided
  if (organization_id) {
    query = query.eq('organization_id', organization_id);
  }

  return await query;
};
