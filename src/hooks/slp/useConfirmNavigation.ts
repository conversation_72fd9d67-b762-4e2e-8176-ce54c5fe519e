'use client';

import { useEffect, useState, useCallback } from 'react';
import { useDisclosure } from '@chakra-ui/react';

export function useConfirmNavigation(when: boolean) {
    const { onOpen, onClose, open } = useDisclosure();

    const [pendingNavigation, setPendingNavigation] = useState<(() => void) | null>(null);

    // Handle browser back/forward navigation
    useEffect(() => {
        if (!when) return;

        const handlePopState = (e: PopStateEvent) => {
            if (when) {
                // Prevent the navigation
                e.preventDefault();
                window.history.pushState(null, '', window.location.href);

                // Show confirmation dialog
                setPendingNavigation(() => () => {
                    // Allow navigation by going back
                    window.history.back();
                });
                onOpen();
            }
        };

        // Add a dummy state to detect back navigation
        window.history.pushState(null, '', window.location.href);
        window.addEventListener('popstate', handlePopState);

        return () => {
            window.removeEventListener('popstate', handlePopState);
        };
    }, [when, onOpen]);

    const confirmNavigation = useCallback(() => {
        onClose();
        if (pendingNavigation) {
            // Execute the pending navigation
            pendingNavigation();
            setPendingNavigation(null);
        }
    }, [onClose, pendingNavigation]);

    const cancelNavigation = useCallback(() => {
        onClose();
        setPendingNavigation(null);
    }, [onClose]);

    // Function to check navigation programmatically
    const checkNavigation = useCallback((navigationFn: () => void) => {
        if (when) {
            setPendingNavigation(() => navigationFn);
            onOpen();
            return false; // Block navigation
        }
        navigationFn(); // Allow navigation
        return true;
    }, [when, onOpen]);

    return {
        confirmNavigation,
        cancelNavigation,
        open,
        checkNavigation
    };
}
