'use client';

import { useEffect, useRef, useState } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useDisclosure } from '@chakra-ui/react';

export function useConfirmNavigation(when: boolean) {
    const router = useRouter();

    const { onOpen, onClose, open } = useDisclosure();

    // Intercept Next.js route changes
    const [pendingPath, setPendingPath] = useState<string | null>(null);

    const pathname = usePathname();
    const lastPathRef = useRef(pathname);

    useEffect(() => {
        if (pathname !== lastPathRef.current) {
            if (when) {
                // block navigation
                onOpen();
                setPendingPath(pathname);

                // restore old path immediately
                router.push(lastPathRef.current!);
            } else {
                // allow navigation
                lastPathRef.current = pathname;
            }
        }
    }, [pathname, when, router, onOpen]);

    const confirmNavigation = () => {
        onClose();
        if (pendingPath) {
            lastPathRef.current = pendingPath;
            router.push(pendingPath);
            setPendingPath(null);
        }
    };

    const cancelNavigation = () => {
        onClose();
        setPendingPath(null);
    };

    return { confirmNavigation, cancelNavigation, open };
}
