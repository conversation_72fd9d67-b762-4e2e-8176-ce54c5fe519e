/* eslint-disable react-hooks/exhaustive-deps */
import {
  getInvoicesByClient,
  useGetInvoicesByClientQuery,
} from '@/api/invoices/get-invoices-by-client-id';
import { tableNames } from '@/constants/table_names';
import supabase from '@/lib/supabase/client';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useGetSingleClientHook } from '../receptionist/contacts/useGetSingleClientHook';
import { ToastMessages } from '@/constants/toast-messages';
import { useDisclosure } from '@chakra-ui/react';
import { convertMinuteToHours } from '@/utils/date-formatter';
import { useGetLinkedClientsQuery } from '@/api/linked_clients/get-linked-clients';
import { useQueryClient } from '@tanstack/react-query';
import { queryKey } from '@/constants/query-key';
import { useRecoilState } from 'recoil';
import { IGetPackagesFilterState } from '@/store/filters/packages';
import { toaster } from '@/components/ui/toaster';
import { getLinkedClientOptionsFromArray } from '@/utils/get-options-from-array';

export const useLinkInvoiceHook = ({ packageData }: any) => {
  const queryClient = useQueryClient();
  const linkDisclosure = useDisclosure();
  const [filter] = useRecoilState(IGetPackagesFilterState);

  const { id } = useParams();

  const clientId = id || packageData?.client_id;

  const { refetch } = useGetSingleClientHook({
    id: String(clientId),
    enabled: linkDisclosure.open,
  });
  // const { refetch: packageRefetch } = useGetAllPackagesApi() as any;

  const existingInvoices = packageData?.invoices;
  const [selectedClientId, setSelectedClientId] = useState(clientId);
  const [invoices, setInvoices] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const [packageStatus, setPackageStatus] = useState('');
  const [predictedBalance, setPredictedBalance] = useState<any>(null);
  const [rowSelection, setRowSelection] = useState<Array<any>>(
    existingInvoices || []
  );

  const headers = [
    'Select',
    'Invoice No',
    ' Date',
    `Session`,
    'Memo',
    'Quantity',
    'Duration',
  ];

  const {
    data: invoicesData,
    isLoading,
    refetch: invoicesRefetch,
  } = useGetInvoicesByClientQuery(
    { id: Number(selectedClientId) },
    {
      enabled: linkDisclosure.open, // Fetch only when the modal is open
    }
  );

  const { data: LinkedClients } = useGetLinkedClientsQuery(Number(id), {
    enabled: Boolean(id),
  });
  const linkedClientOptions: any = getLinkedClientOptionsFromArray(
    LinkedClients || []
  );

  // useMemo(() => {
  //   // linkedClientOptions.push({
  //   //   label: `${packageData?.clients?.first_name} ${packageData?.clients?.last_name}`,
  //   //   value: packageData?.client_id,
  //   // });
  //   const currentClientIsParent = linkedClientOptions.find(
  //     (item: any) => item.client_id === Number(id)
  //   );
  //   if (currentClientIsParent) {
  //     linkedClientOptions.push({
  //       label: `${currentClientIsParent?.clients?.first_name} ${currentClientIsParent?.clients?.last_name}`,
  //       value: currentClientIsParent?.client_id,
  //     });
  //   }

  //   // return [
  //   //   {
  //   //     label: `${packageData?.clients?.first_name} ${packageData?.clients?.last_name}`,
  //   //     value: packageData?.client_id,
  //   //   },
  //   //   ...(Array.isArray(LinkedClients)
  //   //     ? // eslint-disable-next-line no-unsafe-optional-chaining
  //   //       LinkedClients?.filter((item) => Boolean(item.added_client_id))?.map(
  //   //         (item: any) => {
  //   //           if (
  //   //             Number(item.client.id) === Number(id) ||
  //   //             Number(item.added_client.id) === Number(id)
  //   //           ) {
  //   //             return;
  //   //           } else {
  //   //             return {
  //   //               label: ` ${item?.client?.first_name} ${item?.client?.last_name}`,
  //   //               value: item?.client?.id,
  //   //             };
  //   //           }
  //   //         }
  //   //       )
  //   //     : []),
  //   // ];
  // }, [LinkedClients]);

  // const invoiceQueries = useQueries({
  //   queries: linkedClientOptions?.map((item) => ({
  //     queryKey: [queryKey.invoices.getByClients, item.value],
  //     queryFn: () => getInvoicesByClient(item.value),
  //     enabled: Boolean(item.value),
  //   })),
  // });

  const updateInvoices = async () => {
    try {
      setLoading(true);
      const rowSelectionId = rowSelection?.map((item) => item.id);

      const toRemove = existingInvoices
        ?.filter((item: any) => !rowSelectionId.includes(item.id))
        .map((item: any) => item.id);

      // return;
      if (toRemove) {
        await supabase
          .from(tableNames.invoices)
          .update({ package_id: null })
          .in('id', toRemove);
      }
      await supabase
        .from(tableNames.invoices)
        .update({ package_id: packageData?.id })
        .in('id', rowSelectionId);

      // const res = await invoicesRefetch();
      // Invalidate all relevant queries to refetch updated data
      // Step 2: Fetch Updated Data

      const allInvoices = [];
      const tempOptions =
        linkedClientOptions.length > 0
          ? linkedClientOptions
          : [
              {
                label: `any`,
                value: clientId,
              },
            ];
      for (const item of tempOptions) {
        if (item.value) {
          const response = await getInvoicesByClient(item.value); // Call your API

          allInvoices.push(...response); // Save data per client ID
        }
      }

      const thisPackageInvoices = allInvoices?.filter(
        (item: any) => Number(item.package_id) === Number(packageData.id)
      );

      const linkedInvoiceHours = thisPackageInvoices.reduce(
        (sum: any, row: any) => sum + row.total_hours,
        0
      );

      const balanceToRemove = linkedInvoiceHours / packageData.session_duration;
      // return;

      const balance = packageData.package_size - balanceToRemove;
      const resolvedBalance = balance <= 0 ? 0 : balance;

      await supabase
        .from(tableNames.packages)
        .update({ balance })
        .eq('id', packageData?.id);

      const status =
        Number(packageData.package_size - resolvedBalance) ===
        Number(packageData.package_size)
          ? 'COMPLETED'
          : packageStatus
            ? packageStatus
            : 'ACTIVE';

      // Update package status only if it changes
      if (status?.toLowerCase() !== packageData.status?.toLowerCase()) {
        await supabase
          .from(tableNames.packages)
          .update({ status })
          .eq('id', packageData?.id);
      }

      await refetch();
      await invoicesRefetch();
      await queryClient.invalidateQueries({
        queryKey: [queryKey.packages.getAllPackages, filter],
      });

      toaster.create({
        description: ToastMessages.operationSuccess,
        type: 'success',
      });
      linkDisclosure.onClose();
    } catch (error: any) {
      toaster.create({ description: error.message, type: 'error' });
    } finally {
      setLoading(false);
    }
  };
  const updatePackageStatus = async (status: any) => {
    await supabase
      .from(tableNames.packages)
      .update({
        status,
      })
      .eq('id', packageData.id);

    await queryClient.invalidateQueries({
      queryKey: [queryKey.packages.getAllPackages, filter],
    });
    await refetch();
  };

  useEffect(() => {
    setRowSelection(packageData?.invoices || []);
  }, [packageData]);

  const handleInvoiceCheck = (e: any, invoice: any) => {
    const wasSelected = rowSelection?.find(
      (selection) => selection?.id == invoice?.id
    );
    const totalHours =
      rowSelection.reduce(
        (sum, item) => sum + (convertMinuteToHours(item.total_hours) || 0),
        0
      ) + (!wasSelected ? convertMinuteToHours(invoice?.total_hours) : 0);

    let arr = rowSelection;
    console.log(
      'total_hours',
      totalHours,
      Number(
        packageData.package_size *
          convertMinuteToHours(packageData.session_duration)
      ),
      e?.target?.checked
    );
    if (
      !wasSelected &&
      totalHours >
        Number(
          packageData.package_size *
            convertMinuteToHours(packageData.session_duration)
        )
    ) {
      toaster.create({
        description: 'Maximum number of invoice has been selected',
        type: 'error',
      });
      return;
    }

    !wasSelected
      ? (arr = [...arr, invoice])
      : (arr = arr.filter((item) => item.id !== invoice.id));
    setRowSelection(arr);
    const newTotalHrs = arr.reduce(
      (sum, item) => sum + (item.total_hours || 0),
      0
    );
    const toBalance = newTotalHrs / packageData.session_duration;

    setPredictedBalance(toBalance);
  };

  useEffect(() => {
    if (isLoading) {
      return;
    }
    const invoiceDataForPackage = invoicesData?.data
      ?.filter((item: any) => {
        if (item?.package_id && item?.package_id !== packageData?.id) {
          return false;
        } else {
          return true;
        }
      })
      .filter(
        (item: any) =>
          String(item.memo).toLowerCase() !== 'void' &&
          String(item.memo).toLowerCase() !== 'voided'
      );
    setInvoices(invoiceDataForPackage || []);
  }, [invoicesData]);

  return {
    rowSelection,
    setRowSelection,
    invoicesData,
    isLoading,
    headers,
    updateInvoices,
    handleInvoiceCheck,
    loading,
    linkDisclosure,
    invoices,
    updatePackageStatus,
    selectedClientId,
    setSelectedClientId,
    linkedClientOptions,
    predictedBalance,
    packageStatus,
    setPackageStatus,
  };
};
